package com.facishare.crm.fmcg.common.apiname;

public abstract class AccountsPayableNoteFields {

    private AccountsPayableNoteFields() {
    }


    public static final String ORIGIN_NOTE_TYPE = "origin_note_type";
    public static final String ORIGIN_NOTE_TYPE__PURCHASE_ORDER = "purchase_order";
    public static final String ORIGIN_NOTE_TYPE__PURCHASE_RETURN = "purchase_return";

    public static final String CREDIT_SETTLED_AMOUNT = "credit_settled_amount";

    public static final String MATCHED_AMOUNT = "matched_amount";

    public static final String ORIGIN_SOURCE = "origin_source";

    public static final String MATCH_STATUS = "match_status";
    public static final String MATCH_STATUS__NO_MATCH = "no_match";
    public static final String MATCH_STATUS__ALL_MATCH = "all_match";
    public static final String MATCH_STATUS__PART_MATCH = "part_match";


    public static final String CONTACT_OBJECT = "contact_object";
    public static final String CONTACT_OBJECT__SUPPLIER = "supplier";

    public static final String ORIGIN_OBJECT_API_NAME = "origin_object_api_name";

    public static final String SOURCE_NOTE_TYPE = "source_note_type";
    public static final String SOURCE_NOTE_TYPE__PURCHASE_RECEIVED = "purchase_received";
    public static final String SOURCE_NOTE_TYPE__RETURNED_OUTBOUND = "returned_outbound";

    public static final String DEBIT_SETTLED_AMOUNT = "debit_settled_amount";

    public static final String NOTE_DATE = "note_date";


    public static final String SUPPLIER_ID = "supplier_id";

    public static final String ORIGIN_OBJECT_DATA_ID = "origin_object_data_id";

    public static final String BASE = "base";
    public static final String SYSTEM_SOURCE = "system_source";
    public static final String SYSTEM_SOURCE__ACCOUNTS_PAYABLE_SYSTEM = "accounts_payable_system";
    public static final String SYSTEM_SOURCE__PROCUREMENT_SYSTEM = "procurement_system";
    public static final String REMARKS = "remarks";
    public static final String PRE_MATCH_AMOUNT = "pre_match_amount";
    //本次核销金额
    public static final String VIR_THIS_MATCH_AMOUNT = "virThisMatchAmount";


}
