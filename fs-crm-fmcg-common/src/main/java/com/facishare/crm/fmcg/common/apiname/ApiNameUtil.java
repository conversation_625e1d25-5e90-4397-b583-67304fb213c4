package com.facishare.crm.fmcg.common.apiname;

import lombok.SneakyThrows;
import lombok.experimental.UtilityClass;

import java.security.SecureRandom;
import java.util.function.Function;

@UtilityClass
public class ApiNameUtil {

    public static final String CHARS = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWYZ0123456789";

    public static final String ACTIVITY_TYPE_PREFIX = "crmat_";
    public static final String BUDGET_TYPE_PREFIX = "crmbt_";
    public static final String BUDGET_NODE_PREFIX = "crmbn_";
    public static final String API_NAME_SUFFIX = "__c";
    public static final int CODE_SIZE = 5;

    public static boolean isApiName(String apiName) {
        return isBudgetNodeApiName(apiName) || isBudgetTypeApiName(apiName);
    }

    public static boolean isBudgetTypeApiName(String apiName) {
        return apiName.startsWith(BUDGET_TYPE_PREFIX) && apiName.endsWith(API_NAME_SUFFIX);
    }

    public static boolean isBudgetNodeApiName(String apiName) {
        return apiName.startsWith(BUDGET_NODE_PREFIX) && apiName.endsWith(API_NAME_SUFFIX);
    }

    public static String getActivityTypeApiName(Function<String, Boolean> isDuplicateFunc) {
        return get(ACTIVITY_TYPE_PREFIX, isDuplicateFunc);
    }

    public static String getBudgetTypeApiName(Function<String, Boolean> isDuplicateFunc) {
        return get(BUDGET_TYPE_PREFIX, isDuplicateFunc);
    }

    public static String getBudgetNodeApiName(Function<String, Boolean> isDuplicateFunc) {
        return get(BUDGET_NODE_PREFIX, isDuplicateFunc);
    }

    private static String get(String prefix, Function<String, Boolean> isDuplicateFunc) {
        String name;
        do {
            name = get(prefix);
        } while (Boolean.TRUE.equals(isDuplicateFunc.apply(name)));
        return name;
    }

    @SneakyThrows
    private static String get(String prefix) {
        SecureRandom ran = SecureRandom.getInstance("SHA1PRNG");
        StringBuilder builder = new StringBuilder(prefix);
        for (int i = 0; i < CODE_SIZE; i++) {
            builder.append(CHARS.charAt(ran.nextInt(CHARS.length())));
        }
        builder.append(API_NAME_SUFFIX);
        return builder.toString();
    }
}