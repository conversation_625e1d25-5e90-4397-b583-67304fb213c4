package com.facishare.crm.fmcg.common.apiname;

/**
 * <AUTHOR>
 * @date 2022/3/18 下午3:05
 */
public abstract class FMCGSerialNumberStatusFields {

    private FMCGSerialNumberStatusFields() {
    }

    public static final String BUSINESS_OBJECT_ID = "business_object_id";
    public static final String FMCG_SERIAL_NUMBER_ID = "fmcg_serial_number_id";
    public static final String CURRENT_STATE = "current_state";

    public static final String ACTION_ID = "action_id";

    public static final String CURRENT_TENANT_ID = "current_tenant_id";

    public static final String PERSONNEL_ID = "personnel_id";

    public static final String NEXT_BUSINESS_ID = "next_business_id";

    public static final String ACCOUNT_ID = "account_id";

    public static final String CHANNEL_TYPE = "channel_type";

    public static final String BUSINESS_OCCURRENCE_TIME = "business_occurrence_time";

    public static final String PERSONNEL_API_NAME = "personnel_object_api_name";


    public static final String BUSINESS_OBJECT_NAME = "business_object_name";

    public static final String ACCESS_TYPE = "access_type";

    public static final String BUSINESS_OBJECT_NUMBER = "business_object_number";

    public static final String LATITUDE = "latitude";

    public static final String CURRENT_TENANT_NAME = "current_tenant_name";

    public static final String NEXT_BUSINESS_TYPE = "next_business_type";

    public static final String NODE_TYPE = "node_type";

    public static final String NEXT_TENANT_NAME = "next_tenant_name";

    public static final String PERSONNEL_ROLE = "personnel_role";

    public static final String PRODUCT_ID = "product_id";

    public static final String BUSINESS_TYPE = "business_type";

    public static final String PERSONNEL_HOST_ROLE = "personnel_host_role";

    public static final String LONGITUDE = "longitude";

    public static final String SOURCE_SYSTEM = "source_system";


    public static final String PERSONNEL_NAME = "personnel_name";


    public static final String PERSONNEL_HOST_ROLE_NAME = "personnel_host_role_name";


    public static final String WAREHOUSE_NAME = "warehouse_name";

    public static final String NODE_NAME_ID = "node_name_id";


    public static final String BUSINESS_OBJECT = "business_object";


    public static final String NEXT_NODE_ID = "next_node_id";

    public static final String PERSONNEL_PHONE = "personnel_phone";

    public static final String WAREHOUSE_ID = "warehouse_id";

    public static final String EXCEPTION_TYPE = "exception_type";

    public static final String WHETHER_ABNORMAL = "whether_abnormal";

    public static class ChannelType {

        public static final String DEALER = "0";
        public static final String DISTRIBUTE = "1";
        public static final String STORE = "2";
        public static final String CONSUMER = "3";

        public static final String STORE_HOUSE = "5";

        public static final String CONSTITUENT_COMPANY = "4";
    }
}