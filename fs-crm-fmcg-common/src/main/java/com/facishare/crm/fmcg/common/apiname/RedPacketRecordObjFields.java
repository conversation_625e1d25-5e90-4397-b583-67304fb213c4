package com.facishare.crm.fmcg.common.apiname;

public abstract class RedPacketRecordObjFields {

    private RedPacketRecordObjFields() {
    }


    public static final String TRANSFEROR_ACCOUNT = "transferor_account";

    public static final String REWARD_AMOUNT = "reward_amount";

    public static final String RELATED_OBJECT_TENANT_ID = "related_object_tenant_id";

    public static final String TRANSFEROR_TENANT_NAME = "transferor_tenant_name";

    public static final String TRANSFEREE_ID = "transferee_id";

    public static final String TRIGGER_EVENT = "trigger_event";

    public static final String REWARD_PART = "reward_part";

    public static final String REWARD_PART_CODE = "reward_part_code";

    public static final String EVENT_TYPE = "event_type";

    public static final String REWARD_TIME = "reward_time";

    public static final String TRADING_ID = "trading_id";

    public static final String ACTIVITY_ID = "activity_id";
    public static final String ACTIVITY_ID__R = "activity_id__r";

    public static final String REWARDED_PERSON = "rewarded_person";

    public static final String REWARDED_PERSON_ID = "reward_person_id";

    public static final String TRANSFEROR_PHONE = "transferor_phone";

    public static final String TRANSFEREE_PHONE = "transferee_phone";

    public static final String TRANSFEROR_ACCOUNT_TYPE = "transferor_account_type";

    public static final String REWARD_DETAIL_ID = "reward_detail_id";

    public static final String TRANSFEROR_NAME = "transferor_name";

    public static final String PAYMENT_STATUS = "payment_status";

    public static final String TRANSFEROR_WECHAT_OPEN_ID = "transferor_wechat_open_id";

    public static final String TRANSFEROR_WECHAT_APP_ID = "transferor_wechat_app_id";

    public static final String PAYMENT_ERROR_MESSAGE = "payment_error_message";

    public static final String TRANSFEREE_ACCOUNT_TYPE = "transferee_account_type";


    public static final String TRANSFEROR_TENANT_ID = "transferor_tenant_id";

    public static final String RELATED_OBJECT_API_NAME = "related_object_api_name";

    public static final String ACCOUNT_ID = "account_id";

    public static final String ACCOUNT_NAME = "account_name__c";

    public static final String ACCOUNT_TENANT_ID = "account_tenant_id__c";

    public static final String TRANSFEREE_NAME = "transferee_name";

    public static final String RETRY_TIMES = "retry_times";

    public static final String TRANSFEROR_ID = "transferor_id";

    public static final String TRANSFEREE_TENANT_ID = "transferee_tenant_id";

    public static final String TRANSFEREE_TENANT_NAME = "transferee_tenant_name";

    public static final String ORDER_ID = "order_id";

    public static final String BUSINESS_ID = "business_id";

    public static final String DISTRIBUTE_WAY = "distribute_way";

    public static final String WITHDRAWAL_STATUS = "withdrawal_status";

    public static final String RED_PACKET_STATUS = "red_packet_status";

    public static final String EXPIRATION_TIME = "expiration_time";

    public static final String TRANSFEREE_WECHAT_OPEN_ID = "transferee_wechat_open_id";

    public static final String TRANSFEREE_WECHAT_APP_ID = "transferee_wechat_app_id";

    public static final String RELATED_OBJECT_DATA_ID = "related_object_data_id";

    public static final String RELATED_OBJECT_NAME = "related_object_name";

    public static final String SERIAL_NUMBER_STATUS_ID = "serial_number_status_id";

    public static final String REMARKS = "remarks";

    public static final String WITHDRAW_RECORD_ID = "withdraw_record_id";

    public static final String DURATION_DAYS = "duration_days";
    public static final String RED_PACKET_QUERY_STATUS = "red_packet_query_status";

    public static final String TRANSFEREE_WX_UNION_ID = "transferee_wx_union_id";

    public static final String RED_PACKET_TYPE = "red_packet_type";
    public static final String REFERENCE_NOTE_DATA_ID = "reference_note_data_id";
    public static final String REFERENCE_NOTE_API_NAME = "reference_note_api_name";
    public static final String REFERENCE_NOTE_DATA_ID__R = "reference_note_data_id__r";

    // todo 从自定义对象移过来的
    public static final String RECORD_IDENTITY = "record_identity";
    // event_object_tenant_name__c
    public static final String RELATED_OBJECT_TENANT_NAME = "related_object_tenant_name__c";

    public static final String RED_PACKET_LEVEL = "red_packet_level";
    public static final String FROM_CLOUD_ACCOUNT_DEALER_ID = "from_cloud_account_dealer_id__c";
    public static final String FROM_WX_SUB_MCH_ID = "from_wx_sub_mch_id__c";
    public static final String TRANSFEROR_CLOUD_ACCOUNT_DEALER_ID = "transferor_cloud_account_dealer_id";
    public static final String FROM_CLOUD_ACCOUNT_BROKER_ID = "from_cloud_account_broker_id__c";
    public static final String TRANSFEROR_CLOUD_ACCOUNT_BROKER_ID = "transferor_cloud_account_broker_id";

    public static final String ROLE = "role__c";
    public static final String IS_OVER_LIMIT = "is_over_limit__c";


    public static class EventType {
        public static final String REWARD = "activityIncentives";

        public static final String PAY_FOR_EXPENSE = "payForExpense";

        public static final String DISPLAY_CASH = "displayCash";
    }

    public static class TriggerEvent {
        public static final String SCAN_CODE = "scanCode";

        public static final String STORE_REDEEM = "STORE_REDEEM";
        public static final String SEND_COUPONS = "SEND_COUPONS";
    }

    public static class DistributeWay {
        public static final String ARTIFICIAL = "artificial";

        public static final String AUTO = "auto";

    }

    public static class WithdrawalStatus {
        // 待提现
        public static final String AWAIT = "await";

        public static final String PROCESSING = "processing";

        public static final String WITHDRAWN = "withdrawn";

        public static final String NO_NEED = "noNeed";

    }

    public static class RedPacketStatus {
        // 生效中
        public static final String EFFECTUATE = "effectuate";

        public static final String EXPIRED = "expired";

    }

    public static class TransferorAccountType {
        public static final String CLOUD = "cloud";

        public static final String WECHAT_MERCHANT = "wechatMerchant";

        public static final String WECHAT = "wechat";
    }

    public static class TransfereeAccountType {
        public static final String WECHAT = "wechat";

    }

    public static class PaymentStatus {
        public static final String INIT = "0";

        public static final String TRANSFERRING = "1";

        public static final String SUCCESS = "2";

        public static final String FAIL = "3";

        public static final String ERROR = "4";
    }

}