package com.facishare.crm.fmcg.dms.service.abastraction;


import com.alibaba.fastjson.JSON;
import com.facishare.crm.fmcg.common.apiname.CommonFields;
import com.facishare.crm.fmcg.common.utils.QueryDataUtil;
import com.facishare.crm.fmcg.dms.errors.AbandonActionException;
import com.facishare.crm.fmcg.dms.errors.RetryActionException;
import com.facishare.crm.fmcg.dms.model.FinancialBill;
import com.facishare.crm.fmcg.dms.model.PayableConvertResult;
import com.facishare.crm.fmcg.dms.model.TriggerAction;
import com.facishare.crm.fmcg.dms.service.DMSObjectActionService;
import com.facishare.crm.fmcg.dms.web.abstraction.IAccountsPayableService;
import com.facishare.paas.appframework.common.util.StopWatch;
import com.facishare.paas.appframework.config.ConfigService;
import com.facishare.paas.appframework.core.exception.ObjectDataNotFoundException;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.predef.action.BaseObjectSaveAction;
import com.facishare.paas.appframework.metadata.exception.MetaDataBusinessException;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

@Service
@Slf4j
@SuppressWarnings("Duplicates")
public abstract class AutoPayableConvertService implements IAutoConvertService {


    protected static final long LOCK_WAIT = 8;
    protected static final long LOCK_LEASE = 16;
    private static final String LOCK_KEY = "FINANCIAL_BILL_PAYABLE_CONVERT_LOCK.%s.%s";
    @Resource
    protected RedissonClient redissonCmd;
    @Resource
    protected ServiceFacade serviceFacade;
    @Resource
    private DMSObjectActionService dmsObjectActionService;
    @Resource
    private IAccountsPayableService accountsPayableService;
    @Resource
    private ConfigService configService;


    protected abstract void validate(FinancialBill arg);

    protected abstract PayableConvertResult convertData(FinancialBill bill);

    protected abstract void beforeConvert(FinancialBill bill);

    protected IObjectData beanValidate(FinancialBill bill) {
        IObjectData data;
        try {
            data = serviceFacade.findObjectData(User.systemUser(bill.getTenantId()), bill.getId(), bill.getApiName());
        } catch (ObjectDataNotFoundException ex) {
            log.info("data not found,tenantId:{},apiName:{},id:{}", bill.getTenantId(), bill.getApiName(), bill.getId());
            throw new AbandonActionException("data not found");
        } catch (MetaDataBusinessException ex) {
            if ("数据已作废或已删除".equals(ex.getMessage()) && ********* == ex.getErrorCode()) {   //ignorei18n
                log.info("data not found,tenantId:{},apiName:{},id:{}", bill.getTenantId(), bill.getApiName(), bill.getId());
                throw new AbandonActionException("data not found");
            }
            log.error("find object error ", ex);
            throw ex;
        }
        return data;
    }

    @Override
    public void convert(FinancialBill bill) {
        log.info("convert payable started : {}", bill);
        if (denyAccountsPayable(bill.getTenantId())) {
            log.info("denyAccountsPayable.");
            return;
        }
        StopWatch watch = StopWatch.create("FINANCIAL_BILL_PAYABLE_CONVERT." + bill.getApiName() + "." + bill.getId());

        beanValidate(bill);

        beforeConvert(bill);
        watch.lap("payable beforeConvert");

        if (this.tryLock(bill)) {
            watch.lap("payable lock");
            try {
                validate(bill);
                watch.lap("payable validate");

                PayableConvertResult data = this.convertData(bill);
                watch.lap("payable convert");

                save(data);
                watch.lap("payable save");
            } catch (AbandonActionException ex) {
                log.warn("financial bill convert payable abandon : ", ex);
            } catch (RetryActionException ex) {
                log.warn("financial bill convert payable cause retry exception : ", ex);
                throw ex;
            } catch (Exception ex) {
                log.error("financial bill convert payable cause unknown exception : ", ex);
                throw ex;
            } finally {
                this.unlock(bill);
                watch.lap("payable unlock");
            }
        } else {
            throw new RetryActionException("try lock payable financial bill fail");
        }
    }

    private void save(PayableConvertResult convertResult) {
        triggerRemoteActionSave(convertResult);
    }

    private void triggerRemoteActionSave(PayableConvertResult convertResult) {

        for (PayableConvertResult.Payable payable : convertResult.getResult()) {
            log.info("payable object data : {}", JSON.toJSONString(payable));
            TriggerAction.Arg arg = TriggerAction.Arg.builder()
                    .actionName("Add")
                    .apiName(payable.getData().getDescribeApiName())
                    .objectData(payable.getData())
                    .details(payable.getDetails())
                    .detailApiName(payable.getDetails().get(0).getDescribeApiName())
                    .user(User.systemUser(payable.getData().getTenantId()))
                    .triggerFlow(false)
                    .triggerWorkflow(true)
                    .build();

            BaseObjectSaveAction.Result result = dmsObjectActionService.triggerAction(arg);
            log.info("payable convert success : {}", result.getObjectData().getId());
        }


    }


    private void unlock(FinancialBill bill) {
        String key = String.format(LOCK_KEY, bill.getData().getTenantId(), bill.getData().getId());
        RLock lock = redissonCmd.getLock(key);

        log.info("unlock payable financial bill : {}", key);

        if (lock.isLocked() && lock.isHeldByCurrentThread()) {
            lock.unlock();
        }
    }

    private boolean tryLock(FinancialBill bill) {
        String key = String.format(LOCK_KEY, bill.getData().getTenantId(), bill.getData().getId());

        RLock lock = redissonCmd.getLock(key);

        log.info("try lock payable financial bill : {}", key);

        if (lock.isLocked() && lock.isHeldByCurrentThread()) {
            return true;
        }
        try {
            return lock.tryLock(LOCK_WAIT, LOCK_LEASE, TimeUnit.SECONDS);
        } catch (InterruptedException ex) {
            Thread.currentThread().interrupt();
            throw new MetaDataBusinessException(String.format("try lock payable financial bill cause thread interrupted exception : %s", key));
        }
    }


    protected boolean existsPayableByUniqueId(String tenantId, String fieldName, String fieldValue, String apiName) {

        SearchTemplateQuery query = new SearchTemplateQuery();
        query.setOffset(0);
        query.setLimit(1);
        query.setNeedReturnCountNum(false);
        query.setNeedReturnQuote(false);
        query.setSearchSource("db");

        IFilter filter = new Filter();
        filter.setFieldName(fieldName);
        filter.setOperator(Operator.EQ);
        filter.setFieldValues(Lists.newArrayList(fieldValue));

        query.setFilters(Lists.newArrayList(filter));

        List<IObjectData> result = QueryDataUtil.find(
                serviceFacade,
                tenantId,
                apiName,
                query,
                Lists.newArrayList(
                        CommonFields.ID
                )
        );
        return CollectionUtils.isNotEmpty(result);
    }

    protected static boolean zero(BigDecimal subtotal) {
        BigDecimal zero = new BigDecimal("0");
        return zero.compareTo(subtotal) == 0;
    }

    private boolean denyAccountsPayable(String tenantId) {
        return accountsPayableService.denyAccountsPayable(tenantId);
    }
    protected boolean isOpenMultiUnit(String tenantId) {
        String multipleUnit = configService.findTenantConfig(User.systemUser(tenantId), "multiple_unit");
        return Objects.equals("1", multipleUnit);
    }
}
