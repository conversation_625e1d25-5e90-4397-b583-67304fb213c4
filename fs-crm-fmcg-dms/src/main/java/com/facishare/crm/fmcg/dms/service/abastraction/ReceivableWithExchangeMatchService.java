package com.facishare.crm.fmcg.dms.service.abastraction;

import com.alibaba.fastjson.JSON;
import com.facishare.crm.fmcg.common.apiname.*;
import com.facishare.crm.fmcg.common.gray.TPMGrayUtils;
import com.facishare.crm.fmcg.common.utils.QueryDataUtil;
import com.facishare.crm.fmcg.dms.errors.AbandonActionException;
import com.facishare.crm.fmcg.dms.model.FinancialBill;
import com.facishare.crm.fmcg.dms.model.MatchNote;
import com.facishare.crm.fmcg.dms.model.TriggerAction;
import com.facishare.crm.fmcg.dms.service.DMSObjectActionService;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.predef.action.BaseObjectSaveAction;
import com.facishare.paas.appframework.log.ActionType;
import com.facishare.paas.appframework.log.EventType;
import com.facishare.paas.appframework.metadata.dto.SaveMasterAndDetailData;
import com.facishare.paas.metadata.api.DBRecord;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.impl.ObjectData;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;


@Slf4j
@SuppressWarnings("Duplicates")
public abstract class ReceivableWithExchangeMatchService implements IReceivableMathService {

    @Resource
    private ServiceFacade serviceFacade;
    @Resource
    protected DMSObjectActionService dmsObjectActionService;

    protected static final String MATCHABLE_AMOUNT_KEY = "__matchable_amount";
    protected static final String RETURNED_GOODS_INVOICE_ID_KEY = "__returned_goods_invoice_id";

    protected abstract List<IObjectData> queryRelatedData(FinancialBill receivable, String dimensionDataId);

    protected List<IObjectData> filterRelatedData(String tenantId, List<IObjectData> relatedData) {
        List<IObjectData> matchableRelatedData = Lists.newArrayList();
        for (IObjectData relatedDatum : relatedData) {
            BigDecimal matchableAmount = calculateMatchableAmountOfRelatedDatum(tenantId, relatedDatum);
            if (matchableAmount.compareTo(BigDecimal.ZERO) > 0) {
                relatedDatum.set(MATCHABLE_AMOUNT_KEY, matchableAmount);
                matchableRelatedData.add(relatedDatum);
            }
        }
        return matchableRelatedData;
    }

    protected abstract BigDecimal calculateMatchableAmountOfRelatedDatum(String tenantId, IObjectData relatedDatum);

    protected abstract IObjectData buildMatchNoteDetail(FinancialBill receivable, IObjectData receivableDetail, IObjectData relatedDatum, BigDecimal matchAmount);

    protected abstract String verificationMethod();

    protected BigDecimal calculateMaxMatchableAmount(String tenantId, String salesOrderId) {
        return BigDecimal.ONE.negate();
    }

    protected Map<String, List<IObjectData>> groupByDimensionDataId(List<IObjectData> details) {
        return details.stream().filter(detail -> detail.get(RETURNED_GOODS_INVOICE_ID_KEY, String.class) != null)
                .collect(Collectors.groupingBy(detail -> detail.get(RETURNED_GOODS_INVOICE_ID_KEY, String.class)));

    }

    public void receivableMatch(FinancialBill receivable) {
        Map<String, List<IObjectData>> receivableDetailsMap = groupByDimensionDataId(receivable.getDetails());

        for (Map.Entry<String, List<IObjectData>> entry : receivableDetailsMap.entrySet()) {
            String dimensionDataId = entry.getKey();

            List<IObjectData> receivableDetails = this.filterReceivableDetails(receivable.getTenantId(), dimensionDataId, entry.getValue());
            log.info("match start : dimension data id - {}, details - {}", dimensionDataId, JSON.toJSONString(receivableDetails));

            List<IObjectData> relatedData = this.queryRelatedData(receivable, dimensionDataId);
            log.info("related data : {}", JSON.toJSONString(relatedData));

            List<IObjectData> matchableRelatedData = this.filterRelatedData(receivable.getTenantId(), relatedData);
            log.info("matchable related data : {}", JSON.toJSONString(matchableRelatedData));

            if (CollectionUtils.isEmpty(receivableDetails) || CollectionUtils.isEmpty(matchableRelatedData)) {
                continue;
            }

            List<IObjectData> matchNoteDetails = receivableDetails
                    .stream()
                    .flatMap(receivableDetail -> this.receivableDetailMatch(receivable, receivableDetail, matchableRelatedData).stream())
                    .collect(Collectors.toList());
            List<MatchNote> matchNotes = this.buildMatchNotes(receivable, matchNoteDetails);
            log.info("inner match result : {}", JSON.toJSONString(matchNotes));

            this.save(matchNotes);
        }
    }

    private void save(List<MatchNote> notes) {
        for (MatchNote note : notes) {
            save(note);
        }
    }

    private void save(MatchNote note) {
        if (TPMGrayUtils.dmsSkipBusinessSave(note.getData().getTenantId())) {
            // PG Save
            this.localSave(note);
        } else {
            // SFA Add Action
            this.triggerRemoteActionSave(note);
        }
    }

    private void triggerRemoteActionSave(MatchNote note) {
        log.info("final match note object data : {}", JSON.toJSONString(note));

        TriggerAction.Arg arg = TriggerAction.Arg.builder()
                .actionName("Add")
                .apiName(ApiNames.MATCH_NOTE_OBJ)
                .objectData(note.getData())
                .details(note.getDetails())
                .detailApiName(ApiNames.MATCH_NOTE_DETAIL_OBJ)
                .user(User.systemUser(note.getData().getTenantId()))
                .triggerFlow(false)
                .triggerWorkflow(true)
                .build();

        BaseObjectSaveAction.Result result = dmsObjectActionService.triggerAction(arg);

        log.info("match success : {}", result.getObjectData().getId());
    }

    private void localSave(MatchNote note) {
        Map<String, IObjectDescribe> describeMap = Maps.newHashMap();
        describeMap.put(ApiNames.MATCH_NOTE_OBJ, serviceFacade.findObject(note.getData().getTenantId(), ApiNames.MATCH_NOTE_OBJ));
        describeMap.put(ApiNames.MATCH_NOTE_DETAIL_OBJ, serviceFacade.findObject(note.getData().getTenantId(), ApiNames.MATCH_NOTE_DETAIL_OBJ));

        Map<String, List<IObjectData>> detailMap = Maps.newHashMap();
        detailMap.put(ApiNames.MATCH_NOTE_DETAIL_OBJ, note.getDetails());

        SaveMasterAndDetailData.Arg arg = SaveMasterAndDetailData.Arg.builder()
                .objectDescribes(describeMap)
                .masterObjectData(note.getData())
                .detailObjectData(detailMap)
                .build();

        SaveMasterAndDetailData.Result result = serviceFacade.saveMasterAndDetailData(User.systemUser(note.getData().getTenantId()), arg);

        note.setData(result.getMasterObjectData());
        note.setDetails(result.getDetailObjectData().get(ApiNames.MATCH_NOTE_DETAIL_OBJ));

        IObjectDescribe describe = serviceFacade.findObject(note.getData().getTenantId(), note.getData().getDescribeApiName());
        serviceFacade.logWithCustomMessage(
                User.systemUser(note.getData().getTenantId()),
                EventType.ADD,
                ActionType.Add,
                describe,
                note.getData(),
                "自动核销"  //ignorei18n
        );

        log.info("match success : {}", result.getMasterObjectData().getId());
    }

    private List<IObjectData> filterReceivableDetails(String tenantId, String dimensionDataId, List<IObjectData> details) {
        BigDecimal amount = calculateMaxMatchableAmount(tenantId, dimensionDataId);
        List<IObjectData> matchableReceivableDetails = Lists.newArrayList();

        if (amount.compareTo(BigDecimal.ONE.negate()) == 0) {
            for (IObjectData detail : details) {
                BigDecimal matchableAmount = calculateMatchableAmountOfReceivableDetail(tenantId, detail);
                if (matchableAmount.compareTo(BigDecimal.ZERO) >= 0) {
                    detail.set(MATCHABLE_AMOUNT_KEY, matchableAmount);
                    matchableReceivableDetails.add(detail);
                }
            }
        } else {
            for (IObjectData detail : details) {
                BigDecimal matchableAmount = calculateMatchableAmountOfReceivableDetail(tenantId, detail);

                log.info("amount : {}, matchable amount : {}", amount, matchableAmount);

                if (amount.compareTo(matchableAmount) < 0) {
                    matchableAmount = amount;
                }

                if (matchableAmount.compareTo(BigDecimal.ZERO) >= 0) {
                    detail.set(MATCHABLE_AMOUNT_KEY, matchableAmount);
                    matchableReceivableDetails.add(detail);

                    amount = amount.subtract(matchableAmount);

                    if (amount.compareTo(BigDecimal.ZERO) == 0) {
                        break;
                    }
                }
            }
        }

        return matchableReceivableDetails;
    }

    private BigDecimal calculateMatchableAmountOfReceivableDetail(String tenantId, IObjectData detail) {
        BigDecimal receivableAmount = detail.get(AccountsReceivableDetailFields.PRICE_TAX_AMOUNT, BigDecimal.class).abs();
        BigDecimal matchedAmount = calculateMatchedAmountOfReceivableDetail(tenantId, detail.getId());
        BigDecimal matchableAmount = receivableAmount.subtract(matchedAmount);

        log.info("tenant id : {}, api name : {}, id : {}, amount : {}, matched amount : {}, matchable amount : {}",
                tenantId,
                detail.getDescribeApiName(),
                detail.getId(),
                receivableAmount,
                matchedAmount,
                matchableAmount
        );

        return matchableAmount;
    }

    private List<MatchNote> buildMatchNotes(FinancialBill receivable, List<IObjectData> matchNoteDetails) {
        List<MatchNote> matchNotes = Lists.newArrayList();

        Map<String, List<IObjectData>> group = matchNoteDetails.stream().collect(Collectors.groupingBy(
                detail -> {
                    String creditApiName = detail.get(MatchNoteDetailFields.CREDIT_API_NAME, String.class);
                    String creditDataId = detail.get(MatchNoteDetailFields.CREDIT_DATA_ID, String.class);
                    String debitApiName = detail.get(MatchNoteDetailFields.DEBIT_API_NAME, String.class);
                    String debitDataId = detail.get(MatchNoteDetailFields.DEBIT_DATA_ID, String.class);
                    return String.format("MatchNote-%s.%s.%s.%s", creditApiName, creditDataId, debitApiName, debitDataId);
                }
        ));

        for (Map.Entry<String, List<IObjectData>> entry : group.entrySet()) {
            List<IObjectData> details = entry.getValue();

            IObjectData first = details.get(0);

            String creditApiName = first.get(MatchNoteDetailFields.CREDIT_API_NAME, String.class);
            String creditDataId = first.get(MatchNoteDetailFields.CREDIT_DATA_ID, String.class);
            String debitApiName = first.get(MatchNoteDetailFields.DEBIT_API_NAME, String.class);
            String debitDataId = first.get(MatchNoteDetailFields.DEBIT_DATA_ID, String.class);

            IObjectData data = new ObjectData();

            data.setTenantId(receivable.getTenantId());
            data.setOwner(receivable.getData().getOwner());
            data.setCreatedBy(receivable.getData().getCreatedBy());
            data.setRecordType(CommonFields.RECORD_TYPE__DEFAULT);
            data.setDescribeApiName(ApiNames.MATCH_NOTE_OBJ);

            data.set(MatchNoteFields.MATCH_DATE, System.currentTimeMillis());
            data.set(MatchNoteFields.CREDIT_API_NAME, creditApiName);
            data.set(MatchNoteFields.CREDIT_DATA_ID, creditDataId);
            data.set(MatchNoteFields.DEBIT_API_NAME, debitApiName);
            data.set(MatchNoteFields.DEBIT_DATA_ID, debitDataId);
            data.set(MatchNoteFields.ACCOUNT_ID, receivable.getData().get(AccountsReceivableNoteFields.ACCOUNT_ID, String.class));
            data.set(MatchNoteFields.VERIFICATION_METHOD, verificationMethod());

            BigDecimal total = new BigDecimal("0");
            for (IObjectData detail : details) {
                total = total.add(detail.get(MatchNoteDetailFields.THIS_MATCH_AMOUNT, BigDecimal.class));
            }
            data.set(MatchNoteFields.THIS_MATCH_AMOUNT, total);

            BigDecimal creditTotal = new BigDecimal("0");
            for (IObjectData detail : details) {
                BigDecimal creditAmount = detail.get(MatchNoteDetailFields.CREDIT_MATCH_AMOUNT, BigDecimal.class);
                if (Objects.nonNull(creditAmount)) {
                    creditTotal = creditTotal.add(creditAmount);
                }
            }
            data.set(MatchNoteFields.CREDIT_MATCH_AMOUNT, creditTotal);

            matchNotes.add(MatchNote.builder().data(data).details(details).build());
        }

        log.info("match result : {}", JSON.toJSONString(matchNotes));

        return matchNotes;
    }

    private List<IObjectData> receivableDetailMatch(FinancialBill receivable, IObjectData blueReceivableDetail, List<IObjectData> matchableRelatedData) {
        List<IObjectData> matchNoteDetails = Lists.newArrayList();

        matchableRelatedData = matchableRelatedData.stream()
                .sorted(Comparator.comparing(c -> c.get(CommonFields.CREATE_TIME, Long.class)))
                .collect(Collectors.toList());

        BigDecimal remainingAmount = blueReceivableDetail.get(MATCHABLE_AMOUNT_KEY, BigDecimal.class);

        for (IObjectData matchableRelatedDatum : matchableRelatedData) {
            if (remainingAmount.compareTo(BigDecimal.ZERO) == 0) {
                break;
            }
            if (remainingAmount.compareTo(BigDecimal.ZERO) < 0) {
                throw new AbandonActionException("very bad exception, remaining amount can not less than zero.");
            }

            BigDecimal matchableAmountOfRelatedDatum = matchableRelatedDatum.get(MATCHABLE_AMOUNT_KEY, BigDecimal.class);
            BigDecimal matchAmount;

            if (remainingAmount.compareTo(matchableAmountOfRelatedDatum) >= 0) {
                matchAmount = matchableAmountOfRelatedDatum;
            } else {
                matchAmount = remainingAmount;
            }

            remainingAmount = remainingAmount.subtract(matchAmount);
            matchNoteDetails.add(buildMatchNoteDetail(receivable, blueReceivableDetail, matchableRelatedDatum, matchAmount));

            matchableRelatedDatum.set(MATCHABLE_AMOUNT_KEY, matchableAmountOfRelatedDatum.subtract(matchAmount));
        }

        blueReceivableDetail.set(MATCHABLE_AMOUNT_KEY, remainingAmount);

        return matchNoteDetails;
    }

    protected IObjectData buildMatchNoteDetail(
            FinancialBill receivable,
            IObjectData receivableDetail,
            BigDecimal matchAmount,
            BigDecimal creditMatchAmount,
            String creditApiName,
            String creditDataId,
            String creditDetailApiName,
            String creditDetailDataId) {
        IObjectData data = new ObjectData();

        data.setTenantId(receivable.getTenantId());
        data.setOwner(receivable.getData().getOwner());
        data.setCreatedBy(receivable.getData().getCreatedBy());
        data.setRecordType(CommonFields.RECORD_TYPE__DEFAULT);
        data.setDescribeApiName(ApiNames.MATCH_NOTE_DETAIL_OBJ);

        data.set(MatchNoteDetailFields.DEBIT_API_NAME, ApiNames.ACCOUNTS_RECEIVABLE_NOTE_OBJ);
        data.set(MatchNoteDetailFields.DEBIT_DATA_ID, receivableDetail.get(AccountsReceivableDetailFields.AR_ID, String.class));

        data.set(MatchNoteDetailFields.DEBIT_DETAIL_API_NAME, ApiNames.ACCOUNTS_RECEIVABLE_DETAIL_OBJ);
        data.set(MatchNoteDetailFields.DEBIT_DETAIL_DATA_ID, receivableDetail.getId());

        data.set(MatchNoteDetailFields.AR_ID, receivableDetail.get(AccountsReceivableDetailFields.AR_ID, String.class));
        data.set(MatchNoteDetailFields.AR_DETAIL_ID, receivableDetail.getId());

        data.set(MatchNoteDetailFields.CREDIT_API_NAME, creditApiName);
        data.set(MatchNoteDetailFields.CREDIT_DATA_ID, creditDataId);

        data.set(MatchNoteDetailFields.CREDIT_DETAIL_API_NAME, creditDetailApiName);
        data.set(MatchNoteDetailFields.CREDIT_DETAIL_DATA_ID, creditDetailDataId);

        data.set(MatchNoteDetailFields.PRODUCT_ID, receivableDetail.get(AccountsReceivableDetailFields.SKU_ID, String.class));
        data.set(MatchNoteDetailFields.THIS_MATCH_AMOUNT, matchAmount);
        data.set(MatchNoteDetailFields.CREDIT_MATCH_AMOUNT, creditMatchAmount);

        return data;
    }

    protected BigDecimal calculateMatchedAmountOfReceivableDetail(String tenantId, String id) {
        BigDecimal debitAmount = calculateMatchedAmountAsDebit(tenantId, ApiNames.ACCOUNTS_RECEIVABLE_DETAIL_OBJ, id);
        BigDecimal creditAmount = calculateMatchedAmountAsCredit(tenantId, ApiNames.ACCOUNTS_RECEIVABLE_DETAIL_OBJ, id);
        return debitAmount.add(creditAmount);
    }

    protected BigDecimal calculateMatchedAmount(String tenantId, String apiName, String id) {
        BigDecimal debitAmount = calculatePaymentMatchedAmountAsDebit(tenantId, apiName, id);
        BigDecimal creditAmount = calculatePaymentMatchedAmountAsCredit(tenantId, apiName, id);
        return debitAmount.add(creditAmount);
    }

    protected BigDecimal calculatePaymentMatchedAmountAsCredit(String tenantId, String apiName, String id) {
        SearchTemplateQuery query = new SearchTemplateQuery();

        query.setSearchSource("db");
        query.setLimit(1);
        query.setOffset(0);

        Filter creditDataIdFilter = new Filter();
        creditDataIdFilter.setFieldName(MatchNoteDetailFields.CREDIT_DATA_ID);
        creditDataIdFilter.setOperator(Operator.EQ);
        creditDataIdFilter.setFieldValues(Lists.newArrayList(id));

        Filter creditApiNameFilter = new Filter();
        creditApiNameFilter.setFieldName(MatchNoteDetailFields.CREDIT_API_NAME);
        creditApiNameFilter.setOperator(Operator.EQ);
        creditApiNameFilter.setFieldValues(Lists.newArrayList(apiName));

        Filter isDeletedFilter = new Filter();
        isDeletedFilter.setFieldName(CommonFields.IS_DELETED);
        isDeletedFilter.setOperator(Operator.EQ);
        isDeletedFilter.setFieldValues(Lists.newArrayList("false"));

        query.setFilters(Lists.newArrayList(creditDataIdFilter, creditApiNameFilter, isDeletedFilter));

        List<IObjectData> data = serviceFacade.aggregateFindBySearchQueryWithGroupFields(
                User.systemUser(tenantId),
                query,
                ApiNames.MATCH_NOTE_DETAIL_OBJ,
                Lists.newArrayList(MatchNoteDetailFields.CREDIT_DATA_ID, MatchNoteDetailFields.CREDIT_API_NAME),
                "sum",
                MatchNoteDetailFields.THIS_MATCH_AMOUNT
        );

        if (!CollectionUtils.isEmpty(data)) {
            BigDecimal amount = data.get(0).get("sum_" + MatchNoteDetailFields.THIS_MATCH_AMOUNT, BigDecimal.class);
            return Objects.isNull(amount) ? BigDecimal.ZERO : amount.abs();
        } else {
            return new BigDecimal("0");
        }
    }

    protected BigDecimal calculatePaymentMatchedAmountAsDebit(String tenantId, String apiName, String id) {
        SearchTemplateQuery query = new SearchTemplateQuery();

        query.setSearchSource("db");
        query.setLimit(1);
        query.setOffset(0);

        Filter creditDataIdFilter = new Filter();
        creditDataIdFilter.setFieldName(MatchNoteDetailFields.DEBIT_DATA_ID);
        creditDataIdFilter.setOperator(Operator.EQ);
        creditDataIdFilter.setFieldValues(Lists.newArrayList(id));

        Filter creditApiNameFilter = new Filter();
        creditApiNameFilter.setFieldName(MatchNoteDetailFields.DEBIT_API_NAME);
        creditApiNameFilter.setOperator(Operator.EQ);
        creditApiNameFilter.setFieldValues(Lists.newArrayList(apiName));

        Filter isDeletedFilter = new Filter();
        isDeletedFilter.setFieldName(CommonFields.IS_DELETED);
        isDeletedFilter.setOperator(Operator.EQ);
        isDeletedFilter.setFieldValues(Lists.newArrayList("false"));

        query.setFilters(Lists.newArrayList(creditDataIdFilter, creditApiNameFilter, isDeletedFilter));

        List<IObjectData> data = serviceFacade.aggregateFindBySearchQueryWithGroupFields(
                User.systemUser(tenantId),
                query,
                ApiNames.MATCH_NOTE_DETAIL_OBJ,
                Lists.newArrayList(MatchNoteDetailFields.DEBIT_DATA_ID, MatchNoteDetailFields.DEBIT_API_NAME),
                "sum",
                MatchNoteDetailFields.THIS_MATCH_AMOUNT
        );

        if (!CollectionUtils.isEmpty(data)) {
            BigDecimal amount = data.get(0).get("sum_" + MatchNoteDetailFields.THIS_MATCH_AMOUNT, BigDecimal.class);
            return Objects.isNull(amount) ? BigDecimal.ZERO : amount.abs();
        } else {
            return new BigDecimal("0");
        }
    }

    protected BigDecimal calculateMatchedAmountAsDebit(String tenantId, String apiName, String id) {
        SearchTemplateQuery query = new SearchTemplateQuery();

        query.setSearchSource("db");
        query.setLimit(1);
        query.setOffset(0);

        Filter creditDataIdFilter = new Filter();
        creditDataIdFilter.setFieldName(MatchNoteDetailFields.DEBIT_DETAIL_DATA_ID);
        creditDataIdFilter.setOperator(Operator.EQ);
        creditDataIdFilter.setFieldValues(Lists.newArrayList(id));

        Filter creditApiNameFilter = new Filter();
        creditApiNameFilter.setFieldName(MatchNoteDetailFields.DEBIT_DETAIL_API_NAME);
        creditApiNameFilter.setOperator(Operator.EQ);
        creditApiNameFilter.setFieldValues(Lists.newArrayList(apiName));

        Filter isDeletedFilter = new Filter();
        isDeletedFilter.setFieldName(CommonFields.IS_DELETED);
        isDeletedFilter.setOperator(Operator.EQ);
        isDeletedFilter.setFieldValues(Lists.newArrayList("false"));

        query.setFilters(Lists.newArrayList(creditDataIdFilter, creditApiNameFilter, isDeletedFilter));

        List<IObjectData> data = serviceFacade.aggregateFindBySearchQueryWithGroupFields(
                User.systemUser(tenantId),
                query,
                ApiNames.MATCH_NOTE_DETAIL_OBJ,
                Lists.newArrayList(MatchNoteDetailFields.DEBIT_DETAIL_DATA_ID, MatchNoteDetailFields.DEBIT_DETAIL_API_NAME),
                "sum",
                MatchNoteDetailFields.THIS_MATCH_AMOUNT
        );

        if (!CollectionUtils.isEmpty(data)) {
            BigDecimal amount = data.get(0).get("sum_" + MatchNoteDetailFields.THIS_MATCH_AMOUNT, BigDecimal.class);
            return Objects.isNull(amount) ? BigDecimal.ZERO : amount.abs();
        } else {
            return new BigDecimal("0");
        }
    }

    protected BigDecimal calculateMatchedAmountAsCredit(String tenantId, String apiName, String id) {
        SearchTemplateQuery query = new SearchTemplateQuery();

        query.setSearchSource("db");
        query.setLimit(1);
        query.setOffset(0);

        Filter creditDataIdFilter = new Filter();
        creditDataIdFilter.setFieldName(MatchNoteDetailFields.CREDIT_DETAIL_DATA_ID);
        creditDataIdFilter.setOperator(Operator.EQ);
        creditDataIdFilter.setFieldValues(Lists.newArrayList(id));

        Filter creditApiNameFilter = new Filter();
        creditApiNameFilter.setFieldName(MatchNoteDetailFields.CREDIT_DETAIL_API_NAME);
        creditApiNameFilter.setOperator(Operator.EQ);
        creditApiNameFilter.setFieldValues(Lists.newArrayList(apiName));

        Filter isDeletedFilter = new Filter();
        isDeletedFilter.setFieldName(CommonFields.IS_DELETED);
        isDeletedFilter.setOperator(Operator.EQ);
        isDeletedFilter.setFieldValues(Lists.newArrayList("false"));

        query.setFilters(Lists.newArrayList(creditDataIdFilter, creditApiNameFilter, isDeletedFilter));

        List<IObjectData> data = serviceFacade.aggregateFindBySearchQueryWithGroupFields(
                User.systemUser(tenantId),
                query,
                ApiNames.MATCH_NOTE_DETAIL_OBJ,
                Lists.newArrayList(MatchNoteDetailFields.CREDIT_DETAIL_DATA_ID, MatchNoteDetailFields.CREDIT_DETAIL_API_NAME),
                "sum",
                MatchNoteDetailFields.THIS_MATCH_AMOUNT
        );

        if (!CollectionUtils.isEmpty(data)) {
            BigDecimal amount = data.get(0).get("sum_" + MatchNoteDetailFields.THIS_MATCH_AMOUNT, BigDecimal.class);
            return Objects.isNull(amount) ? BigDecimal.ZERO : amount.abs();
        } else {
            return new BigDecimal("0");
        }
    }

    protected List<String> queryNotEnterAccountPaymentIds(String tenantId, Set<String> paymentIds) {
        IFilter idFilter = new Filter();
        idFilter.setFieldName(CommonFields.ID);
        idFilter.setOperator(Operator.IN);
        idFilter.setFieldValues(Lists.newArrayList(paymentIds));

        IFilter enterAccountFilter = new Filter();
        enterAccountFilter.setFieldName(PaymentFields.ENTER_INTO_ACCOUNT);
        enterAccountFilter.setOperator(Operator.EQ);
        enterAccountFilter.setFieldValues(Lists.newArrayList("false"));


        List<IObjectData> payments = QueryDataUtil.find(
                serviceFacade,
                tenantId,
                ApiNames.PAYMENT_OBJ,
                QueryDataUtil.minimumQuery(idFilter, enterAccountFilter),
                Lists.newArrayList(
                        CommonFields.ID
                )
        );

        return payments.stream().map(DBRecord::getId).collect(Collectors.toList());
    }

    protected List<String> queryAllNormalPaymentIds(String tenantId, Set<String> paymentIds) {
        IFilter idFilter = new Filter();
        idFilter.setFieldName(CommonFields.ID);
        idFilter.setOperator(Operator.IN);
        idFilter.setFieldValues(Lists.newArrayList(paymentIds));

        IFilter lifeStatusPaymentFilter = new Filter();
        lifeStatusPaymentFilter.setFieldName(CommonFields.LIFE_STATUS);
        lifeStatusPaymentFilter.setOperator(Operator.EQ);
        lifeStatusPaymentFilter.setFieldValues(Lists.newArrayList(CommonFields.LIFE_STATUS__NORMAL));


        List<IObjectData> payments = QueryDataUtil.find(
                serviceFacade,
                tenantId,
                ApiNames.PAYMENT_OBJ,
                QueryDataUtil.minimumQuery(idFilter, lifeStatusPaymentFilter),
                Lists.newArrayList(
                        CommonFields.ID
                )
        );

        return payments.stream().map(DBRecord::getId).collect(Collectors.toList());
    }

    protected List<String> queryNotOpeningBalancePaymentIds(String tenantId, Set<String> paymentIds) {
        IFilter idFilter = new Filter();
        idFilter.setFieldName(CommonFields.ID);
        idFilter.setOperator(Operator.IN);
        idFilter.setFieldValues(Lists.newArrayList(paymentIds));

        IFilter openingBalanceFilter = new Filter();
        openingBalanceFilter.setFieldName(PaymentFields.OPENING_BALANCE);
        openingBalanceFilter.setOperator(Operator.EQ);
        openingBalanceFilter.setFieldValues(Lists.newArrayList("false"));


        List<IObjectData> payments = QueryDataUtil.find(
                serviceFacade,
                tenantId,
                ApiNames.PAYMENT_OBJ,
                QueryDataUtil.minimumQuery(idFilter, openingBalanceFilter),
                Lists.newArrayList(
                        CommonFields.ID
                )
        );

        return payments.stream().map(DBRecord::getId).collect(Collectors.toList());
    }

    protected List<String> queryNotOpeningBalanceReceivableIds(String tenantId, Set<String> accountsReceivableIds) {
        IFilter idFilter = new Filter();
        idFilter.setFieldName(CommonFields.ID);
        idFilter.setOperator(Operator.IN);
        idFilter.setFieldValues(Lists.newArrayList(accountsReceivableIds));

        IFilter enterAccountFilter = new Filter();
        enterAccountFilter.setFieldName(AccountsReceivableNoteFields.OPENING_BALANCE);
        enterAccountFilter.setOperator(Operator.EQ);
        enterAccountFilter.setFieldValues(Lists.newArrayList("false"));


        List<IObjectData> accountsReceivable = QueryDataUtil.find(
                serviceFacade,
                tenantId,
                ApiNames.ACCOUNTS_RECEIVABLE_NOTE_OBJ,
                QueryDataUtil.minimumQuery(idFilter, enterAccountFilter),
                Lists.newArrayList(
                        CommonFields.ID
                )
        );

        return accountsReceivable.stream().map(DBRecord::getId).collect(Collectors.toList());
    }

    protected List<String> queryExchangeReceivableIds(String tenantId, Set<String> accountsReceivableIds) {
        if (CollectionUtils.isEmpty(accountsReceivableIds)) {
            return Lists.newArrayList();
        }
        Set<String> result = Sets.newHashSet();
        List<IObjectData> accountsReceivables = serviceFacade.findObjectDataByIds(tenantId, Lists.newArrayList(accountsReceivableIds), ApiNames.ACCOUNTS_RECEIVABLE_NOTE_OBJ);

        for (IObjectData accountsReceivable : accountsReceivables) {
            String dataId = accountsReceivable.get(AccountsReceivableNoteFields.OBJECT_RECEIVABLE_DATA_ID, String.class);
            String dataApiName = accountsReceivable.get(AccountsReceivableNoteFields.OBJECT_RECEIVABLE_API_NAME, String.class);
            if (ApiNames.RETURNED_GOODS_INVOICE_OBJ.equals(dataApiName)) {
                IObjectData data = serviceFacade.findObjectData(User.systemUser(tenantId), dataId, dataApiName);
                if (ReturnedGoodsInvoiceFields.EXCHANGE_RECORD.equals(data.getRecordType())) {
                    result.add(accountsReceivable.getId());
                }
            }

        }
        return Lists.newArrayList(result);
    }
}