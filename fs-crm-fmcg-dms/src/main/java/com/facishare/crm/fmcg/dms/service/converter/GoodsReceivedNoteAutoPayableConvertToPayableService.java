package com.facishare.crm.fmcg.dms.service.converter;

import com.facishare.crm.fmcg.common.apiname.*;
import com.facishare.crm.fmcg.common.utils.QueryDataUtil;
import com.facishare.crm.fmcg.dms.errors.AbandonActionException;
import com.facishare.crm.fmcg.dms.errors.BeanNameErrorException;
import com.facishare.crm.fmcg.dms.model.FinancialBill;
import com.facishare.crm.fmcg.dms.model.PayableConvertResult;
import com.facishare.crm.fmcg.dms.service.abastraction.AutoPayableConvertService;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.DBRecord;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.MultiRecordType;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.impl.ObjectData;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Component
@Slf4j
@SuppressWarnings("Duplicates")
public class GoodsReceivedNoteAutoPayableConvertToPayableService extends AutoPayableConvertService {

    public static final String PRICE = "__price";
    public static final String QUANTITY = "__quantity";

    @Override
    protected IObjectData beanValidate(FinancialBill bill) {
        IObjectData receivedNote = super.beanValidate(bill);
        String type = receivedNote.get(GoodsReceivedNoteFields.GOODS_RECEIVED_TYPE, String.class);
        if (!GoodsReceivedNoteFields.GOODS_RECEIVED_TYPE__PURCHASE_RECEIVED.equals(type)) {
            throw new BeanNameErrorException("received type error.");
        }
        return receivedNote;
    }

    @Override
    protected void beforeConvert(FinancialBill bill) {

        bill.setData(serviceFacade.findObjectData(User.systemUser(bill.getTenantId()), bill.getId(), ApiNames.GOODS_RECEIVED_NOTE_OBJ));
        bill.setDetails(queryReceivedNoteDetails(bill.getTenantId(), bill.getData().getId()));

        String purchaseOrderId = bill.getData().get(GoodsReceivedNoteFields.PURCHASE_ORDER_ID, String.class);
        if (Strings.isNullOrEmpty(purchaseOrderId)) {
            throw new AbandonActionException("purchase order id can not be null or empty.");
        }

        IObjectData purchaseOrder = serviceFacade.findObjectData(User.systemUser(bill.getTenantId()), purchaseOrderId, ApiNames.PURCHASE_ORDER_OBJ);
        List<IObjectData> details = queryPurchaseOrderDetails(bill.getTenantId(), purchaseOrder.getId());

        bill.setRelatedBill(FinancialBill.builder()
                .tenantId(bill.getTenantId())
                .apiName(ApiNames.PURCHASE_ORDER_OBJ)
                .data(purchaseOrder)
                .details(details)
                .build());

        detailCalculatePriceAndNumber(bill);
    }

    @Override
    protected void validate(FinancialBill bill) {
        idempotent(bill);
    }

    @Override
    protected PayableConvertResult convertData(FinancialBill bill) {
        return PayableConvertResult.builder().result(Lists.newArrayList(PayableConvertResult.Payable.builder().data(covertToMaster(bill)).details(convertToDetail(bill)).build())).build();
    }

    private IObjectData covertToMaster(FinancialBill bill) {
        IObjectData data = new ObjectData();

        data.setTenantId(bill.getTenantId());
        data.setDescribeApiName(ApiNames.ACCOUNTS_PAYABLE_NOTE_OBJ);
        data.set(CommonFields.CREATE_BY, bill.getData().get(CommonFields.CREATE_BY));
        data.set(CommonFields.OWNER, bill.getData().get(CommonFields.OWNER));
        data.set(CommonFields.RECORD_TYPE, MultiRecordType.RECORD_TYPE_DEFAULT);

        data.set(AccountsPayableNoteFields.BASE, false);
        data.set(AccountsPayableNoteFields.SYSTEM_SOURCE, AccountsPayableNoteFields.SYSTEM_SOURCE__PROCUREMENT_SYSTEM);
        data.set(AccountsPayableNoteFields.NOTE_DATE, System.currentTimeMillis());
        data.set(AccountsPayableNoteFields.SUPPLIER_ID, bill.getRelatedBill().getData().get(PurchaseOrderObjFields.SUPPLIER_ID));
        data.set(AccountsPayableNoteFields.CONTACT_OBJECT, AccountsPayableNoteFields.CONTACT_OBJECT__SUPPLIER);
        data.set(AccountsPayableNoteFields.SOURCE_NOTE_TYPE, AccountsPayableNoteFields.SOURCE_NOTE_TYPE__PURCHASE_RECEIVED);
        data.set(AccountsPayableNoteFields.ORIGIN_NOTE_TYPE, AccountsPayableNoteFields.ORIGIN_NOTE_TYPE__PURCHASE_ORDER);
        data.set(AccountsPayableNoteFields.ORIGIN_OBJECT_DATA_ID, bill.getRelatedBill().getData().getId());
        data.set(AccountsPayableNoteFields.ORIGIN_OBJECT_API_NAME, ApiNames.PURCHASE_ORDER_OBJ);

        return data;
    }

    private List<IObjectData> convertToDetail(FinancialBill bill) {
        BigDecimal subtotalPrice = new BigDecimal("0");
        List<IObjectData> details = Lists.newArrayList();
        for (IObjectData detail : bill.getDetails()) {
            IObjectData data = new ObjectData();
            data.setTenantId(bill.getTenantId());
            data.setDescribeApiName(ApiNames.ACCOUNTS_PAYABLE_DETAIL_OBJ);

            data.set(CommonFields.CREATE_BY, detail.get(CommonFields.CREATE_BY));
            data.set(CommonFields.OWNER, detail.get(CommonFields.OWNER));
            data.set(CommonFields.RECORD_TYPE, CommonFields.RECORD_TYPE__DEFAULT);
            data.set(CommonFields.LIFE_STATUS, CommonFields.LIFE_STATUS__NORMAL);

            data.set(AccountsPayableDetailFields.PRODUCT_ID, detail.get(GoodsReceivedNoteProductFields.PRODUCT_ID));
            BigDecimal price = detail.get(PRICE, BigDecimal.class);
            if (Objects.isNull(price)) {
                continue;
            }
            data.set(AccountsPayableDetailFields.PRICE, price);
            subtotalPrice = subtotalPrice.add(price);
            data.set(AccountsPayableDetailFields.QUANTITY, detail.get(QUANTITY));
            data.set(AccountsPayableDetailFields.GOODS_RECEIVED_NOTE_ID, bill.getData().getId());
            data.set(AccountsPayableDetailFields.GOODS_RECEIVED_NOTE_PRODUCT_ID, detail.getId());
            data.set(AccountsPayableDetailFields.SOURCE_OBJECT_DATA_ID, bill.getRelatedBill().getData().getId());
            data.set(AccountsPayableDetailFields.SOURCE_OBJECT_API_NAME, ApiNames.PURCHASE_ORDER_OBJ);
            data.set(AccountsPayableDetailFields.SOURCE_DETAIL_DATA_ID, detail.get(GoodsReceivedNoteProductFields.PURCHASE_ORDER_PRODUCT_ID));
            data.set(AccountsPayableDetailFields.SOURCE_DETAIL_API_NAME, ApiNames.PURCHASE_ORDER_PRODUCT_OBJ);

            details.add(data);
        }
        if (zero(subtotalPrice)) {
            throw new AbandonActionException("GoodsReceivedNoteAutoPayableConvertToPayableService subtotal is zero.");
        }
        return details;
    }

    private void detailCalculatePriceAndNumber(FinancialBill bill) {
        Map<String, IObjectData> purchaseOrderProductMap = bill.getRelatedBill().getDetails().stream().collect(Collectors.toMap(DBRecord::getId, v -> v, (oldData, newData) -> newData));
        for (IObjectData detail : bill.getDetails()) {

            String purchaseOrderProductId = detail.get(GoodsReceivedNoteProductFields.PURCHASE_ORDER_PRODUCT_ID, String.class);
            IObjectData purchaseOrderProduct = purchaseOrderProductMap.get(purchaseOrderProductId);
            if (Objects.isNull(purchaseOrderProduct)) {
                log.info("purchaseOrderProduct is null,purchaseOrderProductId:{}", purchaseOrderProductId);
                continue;
            }
            if (isOpenMultiUnit(bill.getTenantId())) {
                detail.set(PRICE, purchaseOrderProduct.get(PurchaseOrderProductObjFields.AUXILIARY_PURCHASE_PRICE));
                detail.set(QUANTITY, detail.get(GoodsReceivedNoteProductFields.AUXILIARY_RECEIVED_QUANTITY));
            } else {
                detail.set(PRICE, purchaseOrderProduct.get(PurchaseOrderProductObjFields.PURCHASE_PRICE));
                detail.set(QUANTITY, detail.get(GoodsReceivedNoteProductFields.GOODS_RECEIVED_AMOUNT));
            }

        }
    }

    private List<IObjectData> queryPurchaseOrderDetails(String tenantId, String id) {
        IFilter idFilter = new Filter();
        idFilter.setFieldName(PurchaseOrderProductObjFields.PURCHASE_ORDER_ID);
        idFilter.setOperator(Operator.EQ);
        idFilter.setFieldValues(Lists.newArrayList(id));

        SearchTemplateQuery stq = QueryDataUtil.minimumQuery(idFilter);

        return QueryDataUtil.find(
                serviceFacade,
                tenantId,
                ApiNames.PURCHASE_ORDER_PRODUCT_OBJ,
                stq,
                Lists.newArrayList(
                        CommonFields.ID,
                        CommonFields.NAME,
                        CommonFields.OWNER,
                        CommonFields.CREATE_BY,
                        CommonFields.CREATE_TIME,
                        PurchaseOrderProductObjFields.PURCHASE_PRICE,
                        PurchaseOrderProductObjFields.AUXILIARY_PURCHASE_PRICE
                )
        );
    }

    private List<IObjectData> queryReceivedNoteDetails(String tenantId, String id) {
        IFilter idFilter = new Filter();
        idFilter.setFieldName(GoodsReceivedNoteProductFields.GOODS_RECEIVED_NOTE_ID);
        idFilter.setOperator(Operator.EQ);
        idFilter.setFieldValues(Lists.newArrayList(id));

        SearchTemplateQuery stq = QueryDataUtil.minimumQuery(idFilter);

        return QueryDataUtil.find(
                serviceFacade,
                tenantId,
                ApiNames.GOODS_RECEIVED_NOTE_PRODUCT_OBJ,
                stq,
                Lists.newArrayList(
                        CommonFields.ID,
                        CommonFields.NAME,
                        CommonFields.OWNER,
                        CommonFields.CREATE_BY,
                        CommonFields.CREATE_TIME,
                        GoodsReceivedNoteProductFields.GOODS_RECEIVED_NOTE_ID,
                        GoodsReceivedNoteProductFields.GOODS_RECEIVED_AMOUNT,
                        GoodsReceivedNoteProductFields.AUXILIARY_RECEIVED_QUANTITY,
                        GoodsReceivedNoteProductFields.UNIT,
                        GoodsReceivedNoteProductFields.RETURN_NOTE_PRODUCT_ID,
                        GoodsReceivedNoteProductFields.PRODUCT_ID,
                        GoodsReceivedNoteProductFields.PURCHASE_ORDER_PRODUCT_ID
                )
        );
    }

    private void idempotent(FinancialBill bill) {
        //入库单id
        String goodsReceivedNoteId = bill.getData().getId();
        boolean b = existsPayableByUniqueId(bill.getTenantId(), AccountsPayableDetailFields.GOODS_RECEIVED_NOTE_ID, goodsReceivedNoteId, ApiNames.ACCOUNTS_PAYABLE_DETAIL_OBJ);
        if (b) {
            throw new AbandonActionException(String.format("goodsReceivedNoteId:[%s] is already create AccountsPayable.", goodsReceivedNoteId));
        }
    }
}
