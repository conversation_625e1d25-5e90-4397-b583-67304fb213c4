package com.facishare.crm.fmcg.dms.service.mapper;

import com.facishare.crm.fmcg.common.apiname.ApiNames;
import com.facishare.crm.fmcg.dms.model.FinancialBill;
import com.facishare.crm.fmcg.dms.service.abastraction.IMatchableBillMapService;
import com.facishare.paas.metadata.util.SpringContextUtil;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.Objects;

@Component
@SuppressWarnings("Duplicates")
public class MatchableBillMapper implements IMatchableBillMapService {

    private static final Map<String, String> SUPPORT_OBJECTS = Maps.newHashMap();

    static {
        SUPPORT_OBJECTS.put(ApiNames.PAYMENT_OBJ, "paymentMatchableBillMapService");
        SUPPORT_OBJECTS.put(ApiNames.ACCOUNTS_RECEIVABLE_NOTE_OBJ, "receivableMatchableBillMapService");
        SUPPORT_OBJECTS.put(ApiNames.ACCOUNT_TRANSACTION_FLOW_OBJ, "accountTransactionFlowMatchableBillMapService");
        SUPPORT_OBJECTS.put(ApiNames.ACCOUNTS_PAYABLE_NOTE_OBJ, "accountsPayableMatchableBillMapService");
        SUPPORT_OBJECTS.put(ApiNames.PAY_OBJ, "payMatchableBillMapService");
    }

    public List<FinancialBill> map(FinancialBill bill) {
        String name = SUPPORT_OBJECTS.get(bill.getApiName());
        if (Strings.isNullOrEmpty(name)) {
            return Lists.newArrayList();
        }
        IMatchableBillMapService service = SpringContextUtil.getContext().getBean(name, IMatchableBillMapService.class);
        if (Objects.isNull(service)) {
            return Lists.newArrayList();
        }
        return service.map(bill);
    }
}