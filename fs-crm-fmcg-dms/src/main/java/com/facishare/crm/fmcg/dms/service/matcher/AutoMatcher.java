package com.facishare.crm.fmcg.dms.service.matcher;

import com.facishare.crm.fmcg.common.apiname.ApiNames;
import com.facishare.crm.fmcg.dms.model.FinancialBill;
import com.facishare.crm.fmcg.dms.service.abastraction.IAutoMatchService;
import com.facishare.paas.metadata.util.SpringContextUtil;
import com.google.common.base.Strings;
import com.google.common.collect.Maps;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.Objects;

@Component
@SuppressWarnings("Duplicates")
public class AutoMatcher implements IAutoMatchService {

    private static final Map<String, String> SUPPORT_OBJECTS = Maps.newHashMap();

    static {
        SUPPORT_OBJECTS.put(ApiNames.ACCOUNTS_RECEIVABLE_NOTE_OBJ, "receivableAutoMatchService");
        SUPPORT_OBJECTS.put(ApiNames.ACCOUNTS_PAYABLE_NOTE_OBJ, "payableAutoMatchService");
    }

    public void match(FinancialBill bill) {
        String name = SUPPORT_OBJECTS.get(bill.getApiName());
        if (Strings.isNullOrEmpty(name)) {
            return;
        }
        IAutoMatchService service = SpringContextUtil.getContext().getBean(name, IAutoMatchService.class);
        if (Objects.isNull(service)) {
            return;
        }
        service.match(bill);
    }
}