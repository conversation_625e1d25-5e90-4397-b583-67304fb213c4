package com.facishare.crm.fmcg.fesco.model.fesco;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

public interface CreateTask {

    @Data
    @ToString
    final class Arg implements Serializable {

        private int templateId;

        private String thirdTaskId;

        private String thirdProjectName;

        private String checkMode;

        private List<ChildTaskArg> childTaskList;
    }

    @Data
    @ToString
    final class ChildTaskArg implements Serializable {

        private String name;

        private String idCard;

        private BigDecimal money;

        private String cardNo;

        private String bankId;

        private String content;

        private String thirdSubId;

        private String phone;

        private String signFile;
    }

    @Data
    @ToString
    @EqualsAndHashCode(callSuper = true)
    final class Result extends FescoCommonResult<TaskData> {

    }

    @Data
    @ToString
    final class TaskData implements Serializable {

        private String taskNo;

        private int templateId;

        private String name;

        private String description;

        private String detail;

        private BigDecimal money;

        private BigDecimal serviceMoney;

        private BigDecimal totalMoney;

        private BigDecimal deductibleAmount;

        private BigDecimal paymentAmount;

        private String thirdTaskId;

        private List<ChildTaskData> childTaskList;
    }

    @Data
    @ToString
    final class ChildTaskData implements Serializable {

        private int subTaskId;

        private String idCard;

        private String name;

        private String cardNo;

        private BigDecimal money;

        private BigDecimal subServiceMoney;

        private String thirdSubId;

        private String riskState;

        private String phone;

        private String riskMessage;
    }
}
