package com.facishare.crm.fmcg.service.web.facade;

import com.facishare.crm.fmcg.tpm.web.contract.ActivityTypeTemplateGet;
import com.facishare.crm.fmcg.tpm.web.contract.ActivityTypeTemplateList;
import com.facishare.crm.fmcg.tpm.web.contract.ActivityTypeTemplateNameList;
import com.facishare.crm.fmcg.tpm.web.service.abstraction.IActivityTypeTemplateService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * description : just code
 * <p>
 * create by @yangqf
 * create time 2021/11/15 16:57
 */
@Slf4j
@RestController
@RequestMapping(value = "/TPM/ActivityTypeTemplate", produces = "application/json")
public class ActivityTypeTemplateController {

    @Resource
    private IActivityTypeTemplateService activityTypeTemplateService;

    @PostMapping(value = "List")
    public ActivityTypeTemplateList.Result list(@RequestBody ActivityTypeTemplateList.Arg arg) {
        return activityTypeTemplateService.list(arg);
    }

    @PostMapping(value = "Get")
    public ActivityTypeTemplateGet.Result get(@RequestBody ActivityTypeTemplateGet.Arg arg) {
        return activityTypeTemplateService.get(arg);
    }

    @PostMapping(value = "NameList")
    public ActivityTypeTemplateNameList.Result get(@RequestBody ActivityTypeTemplateNameList.Arg arg) {
        return activityTypeTemplateService.queryNameList(arg);
    }
}