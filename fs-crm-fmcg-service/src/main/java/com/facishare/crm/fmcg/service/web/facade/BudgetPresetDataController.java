package com.facishare.crm.fmcg.service.web.facade;

import com.alibaba.fastjson.JSONObject;
import com.facishare.crm.fmcg.tpm.web.tools.abstraction.IBudgetPresetDataService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * author: wuyx
 * description:
 * createTime: 2022/9/27 18:39
 */
@Slf4j
@RestController
@RequestMapping(value = "/TPM/BudgetPreset", produces = "application/json")
public class BudgetPresetDataController {

    @Resource
    private IBudgetPresetDataService budgetPresetDataService;

    @PostMapping(value = "presetTPMBudgetBusinessSubjectData")
    public JSONObject presetTPMBudgetBusinessSubjectData() {
        budgetPresetDataService.presetTPMBudgetBusinessSubjectData();
        JSONObject result = new JSONObject();
        result.put("success", true);
        return result;
    }
}