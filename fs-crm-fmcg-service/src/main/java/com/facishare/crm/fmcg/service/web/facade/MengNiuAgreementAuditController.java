package com.facishare.crm.fmcg.service.web.facade;

import com.facishare.crm.fmcg.mengniu.service.AgreementAuditService;
import com.facishare.crm.fmcg.tpm.web.contract.AgreementAuditDataList;
import com.facishare.crm.fmcg.tpm.web.contract.AgreementAuditSummaryList;
import com.facishare.crm.fmcg.tpm.web.contract.AgreementItemsAuditList;
import com.facishare.crm.fmcg.tpm.web.contract.OperateAudit;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2024/1/23 14:02
 */
@Slf4j
@RestController
@RequestMapping(value = "/TPM/MengNiu/AuditData", produces = "application/json")
public class MengNiuAgreementAuditController {

    @Resource
    private AgreementAuditService agreementAuditService;

    @PostMapping(value = "AuditSummaryList")
    public AgreementAuditSummaryList.Result auditSummaryList(@RequestBody AgreementAuditSummaryList.Arg arg) {
        return agreementAuditService.auditSummaryList(arg);
    }

    @PostMapping(value = "DataList")
    public AgreementAuditDataList.Result dataList(@RequestBody AgreementAuditDataList.Arg arg) {
        return agreementAuditService.dataList(arg);
    }

    @PostMapping(value = "OperateAudit")
    public OperateAudit.Result operateAudit(@RequestBody OperateAudit.Arg arg) {
        return agreementAuditService.operateAudit(arg);
    }

    @PostMapping(value = "AuditAgreementItemList")
    public AgreementItemsAuditList.Result auditAgreementItemList(@RequestBody AgreementItemsAuditList.Arg arg) {
        return agreementAuditService.auditAgreementItemList(arg);
    }

}
