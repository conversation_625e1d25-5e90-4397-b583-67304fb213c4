package com.facishare.crm.fmcg.service.web.facade;

import com.facishare.crm.fmcg.mengniu.service.StoreAuditService;
import com.facishare.crm.fmcg.tpm.web.contract.OperateAudit;
import com.facishare.crm.fmcg.tpm.web.contract.StoreAuditDataList;
import com.facishare.crm.fmcg.tpm.web.contract.StoreAuditSummaryList;
import com.facishare.crm.fmcg.tpm.web.contract.StoreOperateAudit;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2024/1/23 14:02
 */
@Slf4j
@RestController
@RequestMapping(value = "/TPM/MengNiu/StoreAuditData", produces = "application/json")
public class MengNiuStoreAuditController {
    @Resource
    private StoreAuditService storeAuditService;

    @PostMapping(value = "AuditSummaryList")
    public StoreAuditSummaryList.Result auditSummaryList(@RequestBody StoreAuditSummaryList.Arg arg) {
        return storeAuditService.auditSummaryList(arg);
    }

    @PostMapping(value = "DataList")
    public StoreAuditDataList.Result dataList(@RequestBody StoreAuditDataList.Arg arg) {
        return storeAuditService.dataList(arg);
    }

    @PostMapping(value = "OperateAudit")
    public StoreOperateAudit.Result operateAudit(@RequestBody StoreOperateAudit.Arg arg) {
        return storeAuditService.operateAudit(arg);
    }
}
