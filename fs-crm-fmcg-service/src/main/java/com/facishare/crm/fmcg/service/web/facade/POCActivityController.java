package com.facishare.crm.fmcg.service.web.facade;

import com.facishare.crm.fmcg.tpm.web.contract.poc.POCInitActivity;
import com.facishare.crm.fmcg.tpm.web.contract.poc.POCInitActivityType;
import com.facishare.crm.fmcg.tpm.web.contract.poc.POCInitProof;
import com.facishare.crm.fmcg.tpm.web.poc.abstraction.IPOCActivityService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@Slf4j
@RestController
@RequestMapping(value = "/TPM/POC", produces = "application/json")
public class POCActivityController {
    @Resource
    private IPOCActivityService pocActivityService;

    @PostMapping(value = "/ActivityType/Init")
    public POCInitActivityType.Result init(@RequestBody POCInitActivityType.Arg arg) {
        return pocActivityService.initActivityType(arg);
    }
    @PostMapping(value = "/Activity/Init")
    public POCInitActivity.Result init(@RequestBody POCInitActivity.Arg arg) {
        return pocActivityService.initWholeActivity(arg);
    }
    @PostMapping(value = "/Proof/Create")
    public POCInitProof.Result init(@RequestBody POCInitProof.Arg arg) {
        return pocActivityService.initProof(arg);
    }
}
