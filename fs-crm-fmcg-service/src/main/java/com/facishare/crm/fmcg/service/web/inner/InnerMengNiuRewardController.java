package com.facishare.crm.fmcg.service.web.inner;

import com.facishare.crm.fmcg.mengniu.api.MengNiuRewardRetry;
import com.facishare.crm.fmcg.mengniu.service.IRewardRetryService;
import com.facishare.crm.fmcg.service.web.inner.provider.InnerApiResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@Slf4j
@RestController
@RequestMapping(value = "/TPM/MengNiu/Reward", produces = "application/json")
public class InnerMengNiuRewardController {

    @Resource
    private IRewardRetryService rewardRetryService;

    @PostMapping(value = "Retry")
    public InnerApiResult<MengNiuRewardRetry.Result> retry(@RequestBody MengNiuRewardRetry.Arg arg) {
        return InnerApiResult.apply(rewardRetryService::retry, arg);
    }
}