package com.facishare.crm.fmcg.service.web.inner;

import com.facishare.crm.fmcg.service.web.inner.provider.InnerApiResult;
import com.facishare.crm.fmcg.tpm.api.agreement.YLActivityDetailBulkCreate;
import com.facishare.crm.fmcg.tpm.api.agreement.YLAgreementBulkCreate;
import com.facishare.crm.fmcg.tpm.api.agreement.YLAgreementStatusUpdate;
import com.facishare.crm.fmcg.tpm.business.abstraction.YLTPMService;
import com.facishare.crm.fmcg.tpm.business.abstraction.YLTPMUpgradeService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;


@Slf4j
@RestController
@RequestMapping("/inner/yl/tpm")
public class InnerYLTPMController {

    @Resource
    private YLTPMUpgradeService yltpmUpgradeService;
    @Resource
    private YLTPMService yltpmService;

    @RequestMapping("/data/copy")
    public void dataCopy(@RequestParam String sourceTenantId, @RequestParam String targetTenantId, @RequestParam String flag) {

        yltpmUpgradeService.copy(sourceTenantId, targetTenantId, flag);
    }


    @RequestMapping("/agreement/bulk_create")
    public InnerApiResult<YLAgreementBulkCreate.Result> agreementBulkCreate(@RequestBody YLAgreementBulkCreate.Arg arg) {

        return InnerApiResult.apply(yltpmService::agreementBulkCreate, arg);
    }
    @RequestMapping("/agreement/update_agreement_status")
    public InnerApiResult<YLAgreementStatusUpdate.Result> updateAgreementStatus(@RequestBody YLAgreementStatusUpdate.Arg arg) {

        return InnerApiResult.apply(yltpmService::agreementStatusUpdate, arg);
    }
    @RequestMapping("/activity_detail/bulk_create")
    public InnerApiResult<YLActivityDetailBulkCreate.Result> activityDetailBulkCreate(@RequestBody YLActivityDetailBulkCreate.Arg arg) {

        return InnerApiResult.apply(yltpmService::activityDetailBulkCreate, arg);
    }
}
