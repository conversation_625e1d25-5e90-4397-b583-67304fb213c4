package com.facishare.crm.fmcg.service.web.inner;

import com.facishare.crm.fmcg.service.web.inner.provider.InnerApiResult;
import com.facishare.crm.fmcg.tpm.reward.dto.RewardNotifyRetry;
import com.facishare.crm.fmcg.tpm.reward.outernotify.IRewardsNotifyService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@Slf4j
@RestController
@RequestMapping("/rewards_notify")
public class RewardsNotifyRetryController {

    @Resource
    private IRewardsNotifyService rewardsNotifyService;

    @PostMapping("/retry")
    public InnerApiResult<RewardNotifyRetry.Result> retry(@RequestBody RewardNotifyRetry.Arg arg) {
        return InnerApiResult.apply(rewardsNotifyService::retry, arg);
    }
}