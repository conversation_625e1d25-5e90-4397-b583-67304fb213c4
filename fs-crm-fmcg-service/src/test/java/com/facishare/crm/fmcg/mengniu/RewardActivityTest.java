package com.facishare.crm.fmcg.mengniu;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.facishare.crm.fmcg.common.apiname.ApiNames;
import com.facishare.crm.fmcg.common.apiname.CommonFields;
import com.facishare.crm.fmcg.common.utils.SearchQueryUtil;
import com.facishare.crm.fmcg.mengniu.business.RewardActivityService;
import com.facishare.crm.fmcg.mengniu.dto.ObjectActionEventData;
import com.facishare.crm.fmcg.mengniu.dto.SalesEvent;
import com.facishare.crm.fmcg.mengniu.handler.ObjectActionEventHandler;
import com.facishare.crm.fmcg.tpm.BaseTest;
import com.facishare.crm.fmcg.tpm.api.MengNiuTenantInformation;
import com.facishare.crm.fmcg.tpm.utils.CommonUtils;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.github.trace.TraceContext;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import org.junit.Test;

import javax.annotation.Resource;

public class RewardActivityTest extends BaseTest {

    @Resource
    private RewardActivityService rewardActivityService;
    @Resource
    private ObjectActionEventHandler objectActionEventHandler;
    @Resource
    private ServiceFacade serviceFacade;

    @Test
    public void findTest() {
        MengNiuTenantInformation tenant = MengNiuTenantInformation.builder()
                .role(MengNiuTenantInformation.ROLE_M)
                .manufacturer(MengNiuTenantInformation.builder().tenantId("89150").build())
                .n(MengNiuTenantInformation.builder().tenantId("N").build())
                .m(MengNiuTenantInformation.builder().tenantId("M").build())
                .build();

        IObjectData activity = rewardActivityService.findActivityByStoreAndProduct(
                tenant,
                "64ca1023eacc6100013754f1",
                "64bb4e9878cadf0001eb80c2",
                "consumer_scan_code_rewards__c");

        String name = activity.getName();

        assert activity != null;
    }


    @Test
    public void objectActionTest() {
        String objectId = "65f11a2cb38cae000168d6f3";
//        IObjectData objectData = serviceFacade.findObjectData(User.systemUser("89273"), objectId, "SalesOrderObj");
        IObjectData objectData = serviceFacade.findObjectData(User.systemUser("89273"), objectId, "SalesOrderObj");


        TraceContext context = TraceContext.get();
        if (Strings.isNullOrEmpty(context.getTraceId())) {
            context.setTraceId(String.format("REWARD_OBJECT_MQ.%s.%s.%s.%s", "65e82912bada24000160711f", "SalesOrderObj", objectId, "123"));
        }

        try {

            objectActionEventHandler.invoke(buildObjectActionEvent("i", null, objectData));
        } finally {
            TraceContext.remove();
        }
    }

    private String convertAction(String op) {
        switch (op) {
            case "i":
                return "add";
            case "u":
                return "edit";
            default:
                return "not support";
        }
    }

    private SalesEvent<ObjectActionEventData> buildObjectActionEvent(String op, JSONObject objectBody, IObjectData objectData) {
        ObjectActionEventData objectActionEventData = new ObjectActionEventData();
        String convertOp = convertAction(op);

        objectActionEventData.setObjectAction(convertOp);
        objectActionEventData.setObjectData(objectData);

        SalesEvent<ObjectActionEventData> salesEvent = new SalesEvent<>();
        salesEvent.setData(objectActionEventData);
        salesEvent.setTenantId(objectData.getTenantId());
        salesEvent.setUserId("1000");
        salesEvent.setEventType("USER_OBJECT_ACTION_REWARDS");
        //eventId
        salesEvent.setEventId(String.format("%s.%s.%s.%s.%s", salesEvent.getTenantId(), objectData.getDescribeApiName(), objectActionEventData.getObjectAction(), salesEvent.getEventType(), objectActionEventData.getObjectData().getId().toUpperCase()));
        return salesEvent;
    }


    @Test
    public void testQuery() {
        String tenantId = "89714";
        SearchTemplateQuery queryAgreementQuery = SearchQueryUtil.formSimpleQuery(0, -1, Lists.newArrayList(
                SearchQueryUtil.filter("field_K6Y5h__c.field_vEf7l__c", Operator.IN, Lists.newArrayList("2"), true, null)
        ));
        CommonUtils.executeInAllDataWithFields(serviceFacade, User.systemUser(tenantId), ApiNames.TPM_ACTIVITY_AGREEMENT_OBJ, queryAgreementQuery, Lists.newArrayList(CommonFields.ID, CommonFields.NAME, CommonFields.OBJECT_DESCRIBE_API_NAME), (data) -> {
            System.out.println(JSON.toJSONString(data));
        });
    }
}