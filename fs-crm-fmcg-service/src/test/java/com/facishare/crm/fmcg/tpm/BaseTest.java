package com.facishare.crm.fmcg.tpm;

import com.facishare.crm.fmcg.common.http.ApiContext;
import com.facishare.crm.fmcg.common.http.ApiContextManager;
import com.github.trace.TraceContext;
import org.junit.After;
import org.junit.Before;
import org.junit.runner.RunWith;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import java.util.UUID;

/**
 * description : just code
 * <p>
 * create by @yangqf
 * create time 2021/12/9 21:01
 */
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations = {
        "classpath:spring/application-context-test.xml",
})
public class BaseTest {

    static {
        System.setProperty("process.profile", "fstest");
    }

    @Before
    public void before() {
        ApiContextManager.setContext(ApiContext.builder()
                .tenantId("89150")
                .tenantAccount("89150")
                .employeeId(1000)
                .appId("FMCG_TPM")
                .build());
        String Id = "fs-crm-fmcg-trace:" + UUID.randomUUID().toString();
        System.out.println("traceId:" + Id);
        TraceContext.get().setTraceId(Id);
    }

    @After
    public void after() {
        ApiContextManager.removeContext();
        TraceContext.remove();
    }
}
