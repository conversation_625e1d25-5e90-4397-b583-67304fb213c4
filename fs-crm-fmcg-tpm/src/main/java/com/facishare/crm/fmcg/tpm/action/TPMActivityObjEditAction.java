package com.facishare.crm.fmcg.tpm.action;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.facishare.cep.plugin.exception.BizException;
import com.facishare.crm.fmcg.common.apiname.*;
import com.facishare.crm.fmcg.common.constant.ScanCodeActionConstants;
import com.facishare.crm.fmcg.common.gray.TPMGrayUtils;
import com.facishare.crm.fmcg.common.utils.TPMAllowEditFieldsService;
import com.facishare.crm.fmcg.tpm.api.enumeration.LogType;
import com.facishare.crm.fmcg.tpm.api.log.LogData;
import com.facishare.crm.fmcg.tpm.api.method.IdempotentArgBase;
import com.facishare.crm.fmcg.tpm.api.rule.RewardDetailDTO;
import com.facishare.crm.fmcg.tpm.api.rule.RewardRuleDTO;
import com.facishare.crm.fmcg.tpm.api.rule.UpdateRewardRule;
import com.facishare.crm.fmcg.tpm.business.*;
import com.facishare.crm.fmcg.tpm.business.abstraction.*;
import com.facishare.crm.fmcg.tpm.business.dto.CrmAuditLogDTO;
import com.facishare.crm.fmcg.tpm.business.enums.UseRangeEnum;
import com.facishare.crm.fmcg.tpm.dao.mongo.po.*;
import com.facishare.crm.fmcg.tpm.service.PackTransactionProxyImpl;
import com.facishare.crm.fmcg.tpm.service.abstraction.PackTransactionProxy;
import com.facishare.crm.fmcg.tpm.service.abstraction.TransactionService;
import com.facishare.crm.fmcg.tpm.utils.*;
import com.facishare.crm.fmcg.tpm.web.contract.PromotionPolicyEdit;
import com.facishare.crm.fmcg.tpm.web.contract.model.PersonRewardRuleWhereConditionVO;
import com.facishare.crm.fmcg.tpm.web.manager.ActivityTypeManager;
import com.facishare.crm.fmcg.tpm.web.manager.abstraction.IActivityTypeManager;
import com.facishare.crm.fmcg.tpm.web.manager.abstraction.IRewardRuleManager;
import com.facishare.crm.fmcg.tpm.web.service.PromotionPolicyService;
import com.facishare.crm.fmcg.tpm.web.service.abstraction.IActivityRewardRuleService;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.service.dto.QueryDeptByName;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.common.util.ParallelUtils;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.predef.action.StandardEditAction;
import com.facishare.paas.appframework.metadata.ObjectLifeStatus;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.paas.metadata.util.SpringUtil;
import com.fxiaoke.api.IdGenerator;
import com.fxiaoke.common.MapUtils;
import com.fxiaoke.functions.utils.Maps;
import com.github.autoconf.ConfigFactory;
import com.github.trace.TraceContext;
import com.github.trace.executor.MonitorTaskWrapper;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.Instant;
import java.time.LocalDate;
import java.time.ZoneOffset;
import java.util.*;
import java.util.stream.Collectors;

/**
 * description : just code
 * <p>
 * create by @yangqf
 * create time 2020/11/6 2:27 PM
 */
@SuppressWarnings("Duplicates")
public class TPMActivityObjEditAction extends StandardEditAction implements TransactionService<StandardEditAction.Arg, StandardEditAction.Result> {

    public static final Logger log = LoggerFactory.getLogger(TPMActivityObjEditAction.class);
    public static final BudgetService budgetService = SpringUtil.getContext().getBean(BudgetService.class);
    private static final String SEND_BUDGET_IDS = "SEND_BUDGET_IDS:%s";
    private static final String NEW_ADD_BUDGET = "NEW_ADD_BUDGET";
    private final OperateInfoService operateInfoService = SpringUtil.getContext().getBean(OperateInfoService.class);

    private final PackTransactionProxy packTransactionProxy = SpringUtil.getContext().getBean(PackTransactionProxyImpl.class);

    private ITPM2Service tpm2Service = SpringUtil.getContext().getBean(TPM2Service.class);

    public final IActivityTypeManager activityTypeManager = SpringUtil.getContext().getBean(ActivityTypeManager.class);
    public static final ActivityService activityService = SpringUtil.getContext().getBean(ActivityService.class);

    private final CrmAuditLogService crmAuditLogService = SpringUtil.getContext().getBean(CrmAuditLogService.class);

    private static Map<String, Set<String>> ALLOW_EDIT_DETAIL_FIELD_MAP = new HashMap<>();
    private static Map<String, Set<String>> ALLOW_EDIT_FIELD_MAP = new HashMap<>();

    private final UnifiedActivityCommonLogicBusiness unifiedActivityCommonLogicBusiness = SpringUtil.getContext().getBean(UnifiedActivityCommonLogicBusiness.class);

    private final IRangeFieldBusiness rangeFieldBusiness = SpringUtil.getContext().getBean(IRangeFieldBusiness.class);
    private final PromotionPolicyService promotionPolicyService = SpringUtil.getContext().getBean(PromotionPolicyService.class);

    private final IActivityRewardRuleService activityRewardRuleService = SpringUtil.getContext().getBean(IActivityRewardRuleService.class);

    private final IRewardRuleManager rewardRuleManager = SpringUtil.getContext().getBean(IRewardRuleManager.class);
    private final IPersonnelRewardRuleService personnelRewardRuleService = SpringUtil.getContext().getBean(IPersonnelRewardRuleService.class);

    private final IMengNiuAICalculateService mengNiuAICalculateService = SpringUtil.getContext().getBean(IMengNiuAICalculateService.class);
    public final TPMAllowEditFieldsService tpmAllowEditFieldsService = SpringUtil.getContext().getBean(TPMAllowEditFieldsService.class);
    private final DescribeCacheService describeCacheService = SpringUtil.getContext().getBean(DescribeCacheService.class);

    private final ITPMDisplayReportService tpmDisplayReportService = SpringUtil.getContext().getBean(ITPMDisplayReportService.class);

    private IObjectData unifiedActivity = null;
    private IObjectData activityInDB = null;
    private boolean enableActivityCycleControl = true;
    private Set<String> activityDepartments = new HashSet<>();
    private String ACTIVITY_TYPE_TEMPLATE_ID = "";
    private String PRODUCT_GIFT_DATA = "";
    private Map<String, Object> needUpdatedFieldMap = Maps.newHashMap();

    private RewardRuleDTO rewardRuleData = null;

    private boolean hasEditRewardRule = false;

    private final IEnableCacheService enableCacheService = SpringUtil.getContext().getBean(IEnableCacheService.class);

    private ActivityTypeExt activityTypeExt;

    public static final Map<String, String> TEMPLATE_TENANT_ID = com.google.common.collect.Maps.newHashMap();

    static {
        ConfigFactory.getConfig("fs-fmcg-tpm-config", iConfig -> {
            String json = iConfig.get("activity_allow_edit_detail_field_map");
            if (!Strings.isNullOrEmpty(json)) {
                ALLOW_EDIT_DETAIL_FIELD_MAP = JSON.parseObject(json, new TypeReference<Map<String, Set<String>>>() {
                });
            }

            String editFieldJSON = iConfig.get("activity_allow_edit_field_map");
            if (!Strings.isNullOrEmpty(editFieldJSON)) {
                ALLOW_EDIT_FIELD_MAP = JSON.parseObject(editFieldJSON, new TypeReference<Map<String, Set<String>>>() {
                });
            }
        });

        ConfigFactory.getConfig("gray-rel-fmcg", conf -> {
            String json = conf.get("mengniu_template_tenant_config");
            if (!Strings.isNullOrEmpty(json)) {
                try {
                    JSONObject templateTenant = JSON.parseObject(json);
                    String n = templateTenant.getString("n");
                    String m = templateTenant.getString("m");
                    if (!org.springframework.util.StringUtils.isEmpty(n)) {
                        TEMPLATE_TENANT_ID.put("1", n);
                    }
                    if (!org.springframework.util.StringUtils.isEmpty(m)) {
                        TEMPLATE_TENANT_ID.put("2", m);
                    }
                } catch (Exception ex) {
                    log.error("mengniu template tenant id config parse error : ", ex);
                }
            }
        });
    }

    @Override
    protected void before(Arg arg) {
        //日志
        sendActivityObjAuditLog(arg);
        fillArgData(arg);
        super.before(arg);
        initData();
        validateBrand();
        validateRewardRule();
        validateDateRange(arg);
        validateData();
        setDefaultValue(arg);
        validateUnifiedActivity();
        validateActivityStatus(arg);
        actionContext.setAttribute("triggerFlow", true);
        validateStatus(arg);
        validateCashingProduct(arg);
        buildApprovalCallback();
        validateProductRange();
        validateAI();
        validateOrderProductRule();
        validateEnableEdit(arg);
        validateMaxWriteOffCount(arg);
        validateEnableEditPricePolicy(arg);
        validateActivityDetail();
        validateDisplayReport(arg);
    }

    private void validateActivityDetail() {
        if (!MapUtils.isNullOrEmpty(this.detailObjectData)) {
            if (detailObjectData.containsKey(ApiNames.TPM_ACTIVITY_PRIZES_OBJ)) {
                List<IObjectData> details = detailObjectData.get(ApiNames.TPM_ACTIVITY_PRIZES_OBJ);
                details.forEach(detail -> {
                    String goodType = detail.get(TPMActivityPrizesFields.PRIZE_TYPE + "__v", String.class);
                    if (Strings.isNullOrEmpty(goodType)) {
                        throw new ValidateException(I18N.text(I18NKeys.ACTIVITY_PRIZE_TYPE_CAN_NOT_NULL));
                    }
                    if (PointsGoodsFields.CommodityType.ONE_MORE_GOODS.equals(goodType)
                            && org.apache.commons.collections4.CollectionUtils.isEmpty(detail.get(TPMActivityPrizesFields.PRIZE_PRODUCT_IDS, List.class))) {
                        throw new ValidateException(I18N.text(I18NKeys.ONE_MORE_BOX_REWARD_HAS_NO_PRODUCT));
                    }
                    if (Objects.isNull(detail.get(TPMActivityPrizesFields.PRIZE_AMOUNT))) {
                        if (PointsGoodsFields.CommodityType.RED_PACKET.equals(goodType)) {
                            throw new ValidateException(I18N.text(I18NKeys.PRIZE_DETAIL_RED_PACKET_NEED_PRIZE_AMOUNT));
                        } else if (Boolean.TRUE.equals(detail.get(TPMActivityPrizesFields.PRIZE_AMOUNT_VERIFIED_BY_STORE))) {
                            throw new ValidateException(I18N.text(I18NKeys.PRIZE_AMOUNT_CAN_NOT_EMPTY_DUE_TO_WRITE_OF));
                        }
                    }
                });
            }
        }
        if (!CollectionUtils.isEmpty(this.detailsToDelete)) {
            this.detailsToDelete.forEach(detail -> {
                if (detail.getDescribeApiName().equals(ApiNames.TPM_ACTIVITY_PRIZES_OBJ) && detail.get(TPMActivityPrizesFields.DISTRIBUTED_QUANTITY, BigDecimal.class, BigDecimal.ZERO).intValue() > 0) {
                    throw new ValidateException(I18N.text(I18NEnums.FORBIDDEN_DELETE_ACTIVITY_REWARD.getCode()));
                }
            });
        }
    }

    private void validateAI() {
        mengNiuAICalculateService.validateAIRewardRule((String) arg.getObjectData().get("mn_ai_rule__c"));
    }

    private void validateOrderProductRule() {
        mengNiuAICalculateService.validateOrderProductRule(arg.getObjectData().toObjectData().get("mn_order_product_rule__c", String.class));
    }

    private void validateBrand() {
        IObjectData data = arg.getObjectData().toObjectData();
        if (activityTypeExt != null) {
            String customerType = data.get(TPMActivityFields.CUSTOMER_TYPE, String.class);
            if (Boolean.TRUE.equals(activityTypeExt.get().getForbidRelateCustomer())) {
                if (!ActivityCustomerTypeEnum.BRAND.value().equals(customerType)) {
                    throw new ValidateException(I18N.text(I18NKeys.ACTIVITY_OBJ_EDIT_ACTION_0));
                }
                String storeRange = data.get(TPMActivityFields.STORE_RANGE, String.class);
                if (Strings.isNullOrEmpty(storeRange) || !storeRange.contains("ALL")) {
                    throw new ValidateException(I18N.text(I18NKeys.ACTIVITY_OBJ_EDIT_ACTION_1));
                }
            } else {
                if (ActivityCustomerTypeEnum.BRAND.value().equals(customerType)) {
                    throw new ValidateException(I18N.text(I18NKeys.ACTIVITY_OBJ_EDIT_ACTION_2));
                }
            }

        }
    }

    private void validateEnableEditPricePolicy(Arg arg) {
        ActivityTypePO activityTypePO = activityTypeExt.get();
        String templateId = activityTypePO.getTemplateId();
        if (templateId == null || !templateId.startsWith("promotion")) {
            return;
        }

        //判断web端请求 WEB.chrome
        String clientInfo = actionContext.getRequestContext().getClientInfo();
        log.info("clientInfo :{}", clientInfo);
        if (!clientInfo.startsWith("WEB")) {
            throw new ValidateException(I18N.text(I18NKeys.ACTIVITY_OBJ_EDIT_ACTION_3));
        }

        IObjectData activity = arg.getObjectData().toObjectData();
        BigDecimal activityAmount = activity.get(TPMActivityFields.ACTIVITY_AMOUNT, BigDecimal.class, BigDecimal.ZERO);
        if (activityAmount.compareTo(BigDecimal.ZERO) <= 0) {
            throw new ValidateException(I18N.text(I18NKeys.ACTIVITY_OBJ_EDIT_ACTION_4));
        }

        JSONObject storeRange = JSON.parseObject(activity.get(TPMActivityFields.STORE_RANGE, String.class));
        String storeRangeType = storeRange.getString("type");
        String limitObjType = activity.get(TPMActivityFields.LIMIT_OBJ_TYPE, String.class);
        String rioActivityId = activity.get(TPMActivityFields.RIO_ACTIVITY_ID, String.class);
        if (StringUtils.isNotEmpty(rioActivityId)) {
            if (!TPMActivityFields.LIMIT_OBJ_TYPE_ACTIVITY_AMOUNT_TOTAL.equals(limitObjType)) {
                throw new ValidateException(I18N.text(I18NKeys.ACTIVITY_OBJ_EDIT_ACTION_5));
            }
        }
        switch (limitObjType) {
            case TPMActivityFields.LIMIT_OBJ_TYPE_ACTIVITY_AMOUNT_TOTAL:
                break;
            case TPMActivityFields.LIMIT_OBJ_TYPE_ACCOUNT_COST_EQUAL:
                BigDecimal amountUnifyLimitAmount = activity.get(TPMActivityFields.ACCOUNT_UNIFY_LIMIT_AMOUNT, BigDecimal.class, BigDecimal.ZERO);
                if (amountUnifyLimitAmount.compareTo(BigDecimal.ZERO) <= 0) {
                    throw new ValidateException(I18N.text(I18NKeys.ACTIVITY_OBJ_EDIT_ACTION_6));
                }
                if (activityAmount.compareTo(amountUnifyLimitAmount) < 0) {
                    throw new ValidateException(I18N.text(I18NKeys.ACTIVITY_OBJ_EDIT_ACTION_7));
                }
                break;
            case TPMActivityFields.LIMIT_OBJ_TYPE_ACCOUNT_COST_DIFFERENCE:
                if (!UseRangeEnum.FIXED.value().equals(storeRangeType)) {
                    throw new ValidateException(I18N.text(I18NKeys.ACTIVITY_OBJ_EDIT_ACTION_8));
                }
                List<ObjectDataDocument> details = arg.getDetails().get(ApiNames.TPM_ACTIVITY_STORE_OBJ);
                if (CollectionUtils.isEmpty(details)) {
                    throw new ValidateException(I18N.text(I18NKeys.ACTIVITY_OBJ_EDIT_ACTION_9));
                }
                //统计参与活动客户促销总额
                BigDecimal totalPolicyLimitAmount = BigDecimal.ZERO;
                for (ObjectDataDocument document : details) {
                    BigDecimal policyLimitAmount = document.toObjectData().get(TPMActivityStoreFields.POLICY_LIMIT_AMOUNT, BigDecimal.class, BigDecimal.ZERO);
                    if (policyLimitAmount.compareTo(BigDecimal.ZERO) <= 0) {
                        throw new ValidateException(I18N.text(I18NKeys.ACTIVITY_OBJ_EDIT_ACTION_10));
                    }
                    totalPolicyLimitAmount = totalPolicyLimitAmount.add(policyLimitAmount);
                }
                if (activityAmount.compareTo(totalPolicyLimitAmount) < 0) {
                    throw new ValidateException(I18N.text(I18NKeys.ACTIVITY_OBJ_EDIT_ACTION_11));
                }
                break;
            default:
        }
        PromotionPolicyEdit.Result result = promotionPolicyService.doEnableEditPromotionPolicy(actionContext.getTenantId(), arg.getObjectData().toObjectData());
        if (Boolean.FALSE.equals(result.getEnable())) {
            throw new ValidateException(result.getMsg() == null ? I18N.text(I18NKeys.ACTIVITY_OBJ_EDIT_ACTION_12) : result.getMsg());
        }

        PRODUCT_GIFT_DATA = (String) CommonUtils.getOrDefault(arg.getObjectData().get("product_gift_data_json"), "");

    }

    private void validateMaxWriteOffCount(Arg arg) {
        IObjectData data = arg.getObjectData().toObjectData();
        String maxWriteOffCount = data.get(TPMActivityFields.MAX_WRITE_OFF_COUNT, String.class);
        String dealerId = data.get(TPMActivityFields.DEALER_ID, String.class);
        if (ActivityMaxWriteOffCountEnum.ONCE.value().equals(maxWriteOffCount)
                && Strings.isNullOrEmpty(dealerId)
                && !TPMGrayUtils.skipActivityOnceWriteOffValidate(actionContext.getTenantId())) {
            throw new ValidateException(I18N.text(I18NKeys.ACTIVITY_OBJ_EDIT_ACTION_13));
        }
    }

    private void validateData() {
        if (this.dbMasterData != null) {
            if (TPMActivityFields.CLOSE_STATUS__CLOSED.equals(this.dbMasterData.get(TPMActivityFields.CLOSE_STATUS))) {
                throw new ValidateException(I18N.text(I18NKeys.ACTIVITY_OBJ_EDIT_ACTION_14));
            }

            if (TPMGrayUtils.allowEditActivityPreField(actionContext.getTenantId())) {
                Long endDate = this.dbMasterData.get(TPMActivityFields.END_DATE, Long.class);
                long currentDate = System.currentTimeMillis();
                if (currentDate > endDate) {
                    throw new ValidateException(I18N.text(I18NKeys.ACTIVITY_OBJ_EDIT_ACTION_15));
                }
            }
        }
    }

    private void fillArgData(Arg arg) {
        //日期为空，表示默认值。
        ObjectDataDocument objectData = arg.getObjectData();
        if (objectData.containsKey(TPMDealerActivityCostFields.BEGIN_DATE)) {
            objectData.putIfAbsent(TPMDealerActivityCostFields.BEGIN_DATE, TimeUtils.MIN_DATE);
        }
        if (objectData.containsKey(TPMDealerActivityCostFields.END_DATE)) {
            objectData.putIfAbsent(TPMDealerActivityCostFields.END_DATE, TimeUtils.MAX_DATE);
        }
        if (Boolean.TRUE.equals(objectData.get("is_ignore_activity_store__c"))) {
            log.info("ignore activity store obj.");
            arg.getDetails().remove(ApiNames.TPM_ACTIVITY_STORE_OBJ);
        }
    }

    private void validateCashingProduct(Arg arg) {
        if (!TPMGrayUtils.activityCashingProductIsRequired(actionContext.getTenantId())) {
            return;
        }
        String dealerCashType = (String) arg.getObjectData().get(TPMActivityFields.DEALER_CASHING_TYPE);
        String storeCashType = (String) arg.getObjectData().get(TPMActivityFields.STORE_CASHING_TYPE);
        if (Objects.equals(dealerCashType, TPMActivityCashingProductFields.GOODS) || Objects.equals(storeCashType, TPMActivityCashingProductFields.GOODS)) {
            List<ObjectDataDocument> details = arg.getDetails().get(ApiNames.TPM_ACTIVITY_CASHING_PRODUCT_OBJ);
            if (org.apache.commons.collections4.CollectionUtils.isEmpty(details)) {
                throw new ValidateException(I18N.text(I18NKeys.ACTIVITY_OBJ_EDIT_ACTION_16));
            }
        }
    }

    private void validateDisplayReport(Arg arg) {
        try {
            tpmDisplayReportService.validateDisplayReport(arg);
        } catch (ValidateException ex) {
            throw ex;
        } catch (Exception e) {
            throw new ValidateException(e.getMessage() != null ? e.getMessage() : "System Validate Error.");
        }
    }

    private void initData() {
        needUpdatedFieldMap.putAll(this.updatedFieldMap);
        String unifiedActivityId = this.arg.getObjectData().toObjectData().get(TPMActivityFields.ACTIVITY_UNIFIED_CASE_ID, String.class);
        this.unifiedActivity = Strings.isNullOrEmpty(unifiedActivityId) ? null : serviceFacade.findObjectData(actionContext.getUser(), unifiedActivityId, ApiNames.TPM_ACTIVITY_UNIFIED_CASE_OBJ);
        this.activityInDB = serviceFacade.findObjectData(actionContext.getUser(), arg.getObjectData().getId(), ApiNames.TPM_ACTIVITY_OBJ);
        String jsonData = this.arg.getObjectData().toObjectData().get(TPMActivityFields.REWARD_RULE_JSON, String.class);
        if (!Strings.isNullOrEmpty(jsonData)) {
            rewardRuleData = JSON.parseObject(jsonData, RewardRuleDTO.class);
            rewardRuleData.setTenantId(actionContext.getTenantId());
        }

        String activityTypeId = this.arg.getObjectData().toObjectData().get(TPMActivityFields.ACTIVITY_TYPE, String.class);
        if (Strings.isNullOrEmpty(activityTypeId)) {
            throw new ValidateException(I18N.text(I18NKeys.ACTIVITY_OBJ_EDIT_ACTION_17));
        }
        log.info("activity type id is {}", activityTypeId);
        this.activityTypeExt = activityTypeManager.find(actionContext.getTenantId(), activityTypeId);
        ACTIVITY_TYPE_TEMPLATE_ID = this.activityTypeExt.get().getTemplateId();
        ActivityPlanConfigEntity activityPlanConfig = activityTypeExt.activityPlanConfig();
        if (activityPlanConfig != null && activityPlanConfig.getEnableActivityCycleControl() != null) {
            this.enableActivityCycleControl = activityPlanConfig.getEnableActivityCycleControl();
            log.info("activity enableActivityCycleControl is {}", this.enableActivityCycleControl);
        }
    }

    private void setDefaultValue(Arg arg) {
        if (Strings.isNullOrEmpty((String) arg.getObjectData().get(TPMActivityFields.STORE_RANGE))) {
            arg.getObjectData().put(TPMActivityFields.STORE_RANGE, "{\"type\":\"ALL\",\"value\":\"ALL\"}");
        }

        List<String> departmentIds = new ArrayList<>();
        if (arg.getObjectData().toObjectData().get(TPMActivityFields.DEPARTMENT_RANGE) != null) {
            departmentIds.addAll((List<String>) arg.getObjectData().toObjectData().get(TPMActivityFields.DEPARTMENT_RANGE));
        }
        // 部门默认值
        suppleActivityDepartmentRange();
        // 参与部门处理。
        List<String> departmentRangeList = CommonUtils.castIgnore(arg.getObjectData().toObjectData().get(TPMActivityFields.MULTI_DEPARTMENT_RANGE), String.class);
        if (!CollectionUtils.isEmpty(departmentRangeList)) {
            departmentIds.addAll(departmentRangeList);
        }
        if (this.updatedFieldMap.containsKey(TPMActivityFields.STORE_RANGE) || this.updatedFieldMap.containsKey(TPMActivityFields.DEALER_ID)) {
            String newValue = rangeFieldBusiness.newActivityStoreRangeCondition(actionContext.getTenantId(), this.objectData.get(TPMActivityFields.CUSTOMER_TYPE, String.class, ActivityCustomerTypeEnum.DEALER_STORE.value()), this.objectData.get(TPMActivityFields.STORE_RANGE, String.class), this.objectData.get(TPMActivityFields.DEALER_ID, String.class));
            this.objectData.set(TPMActivityFields.STORE_RANGE, newValue);
            this.updatedFieldMap.put(TPMActivityFields.STORE_RANGE, newValue);
        }
        serviceFacade.getSubDeptsByDeptIds(actionContext.getTenantId(), User.SUPPER_ADMIN_USER_ID, departmentIds, 0).values().forEach(v ->
                activityDepartments.addAll(v.stream().map(QueryDeptByName.DeptInfo::getId).collect(Collectors.toList())));
    }

    private void suppleActivityDepartmentRange() {
        IObjectData objectData = arg.getObjectData().toObjectData();
        List<String> departmentRangeList = CommonUtils.castIgnore(objectData.get(TPMActivityFields.MULTI_DEPARTMENT_RANGE), String.class);
        if (CollectionUtils.isEmpty(departmentRangeList) || departmentRangeList.contains("999998")) {
            List<String> departmentIds = Lists.newArrayList("999999");
            //当申请关联方案时，参与部门为空，默认为方案上的参与部门范围。
            if (objectData.get(TPMActivityFields.ACTIVITY_UNIFIED_CASE_ID) != null && unifiedActivity != null) {
                departmentIds = CommonUtils.castIgnore(unifiedActivity.get(TPMActivityUnifiedCaseFields.ACTIVITY_DEPARTMENT_RANGE), String.class);
            }
            objectData.set(TPMActivityFields.MULTI_DEPARTMENT_RANGE, departmentIds);
            if (this.updatedFieldMap.containsKey(TPMActivityFields.MULTI_DEPARTMENT_RANGE)) {
                this.updatedFieldMap.put(TPMActivityFields.MULTI_DEPARTMENT_RANGE, departmentIds);
            }
        }
    }

    private void validateUnifiedActivity() {
        if (this.unifiedActivity != null) {
            String unifiedActivityCloseStatus = this.unifiedActivity.get(TPMActivityUnifiedCaseFields.CLOSE_STATUS, String.class);
            if (TPMActivityUnifiedCaseFields.CLOSE_STATUS__CLOSED.equals(unifiedActivityCloseStatus)) {
                throw new ValidateException(I18N.text(I18NKeys.ACTIVITY_OBJ_EDIT_ACTION_18));
            }
            IObjectData data = this.arg.getObjectData().toObjectData();
            unifiedActivityCommonLogicBusiness.checkIfUnifiedActivityContainsActivityType(this.unifiedActivity, data.get(TPMActivityFields.ACTIVITY_TYPE, String.class));
            String dealerId = data.get(TPMActivityFields.DEALER_ID, String.class);
            if (!Strings.isNullOrEmpty(dealerId)) {
                IObjectData dealer = serviceFacade.findObjectData(actionContext.getUser(), dealerId, ApiNames.ACCOUNT_OBJ);
                unifiedActivityCommonLogicBusiness.checkDealerIsInTheRangeOfUnifiedActivityLimits(actionContext.getTenantId(), this.unifiedActivity, dealer);
            }
            unifiedActivityCommonLogicBusiness.checkDepartmentIsUnderTheRangeOfUnifiedActivityLimits(actionContext.getTenantId(), unifiedActivity, new ArrayList<>(activityDepartments));
        }
    }

    private void validateStatus(Arg arg) {
        String lifeStatus = (String) arg.getObjectData().get(CommonFields.LIFE_STATUS);
        String budgetId = (String) arg.getObjectData().get(TPMActivityFields.BUDGET_TABLE);
        if (!Strings.isNullOrEmpty(budgetId) && (ObjectLifeStatus.IN_CHANGE.getCode().equals(lifeStatus) || ObjectLifeStatus.UNDER_REVIEW.getCode().equals(lifeStatus))) {
            throw new ValidateException(I18N.text(I18NKeys.ACTIVITY_OBJ_EDIT_ACTION_19));
        }
    }

    @Override
    protected void diffObjectDataWithDbData() {
        super.diffObjectDataWithDbData();
        buildApprovalCallback();
        AddApprovalCallback(GlobalConstant.BUDGET_APPROVAL_CALLBACK_INCREASE_AMOUNT_KEY);
    }

    private void buildApprovalCallback() {
        if (!MapUtils.isNullOrEmpty(this.updatedFieldMap)) {
            if (this.updatedFieldMap.containsKey(GlobalConstant.BUDGET_APPROVAL_CALLBACK_MAP_KEY))
                return;
            Map<String, Object> callbackDatum = new HashMap<>();
            String repeatValue = actionContext.getAttribute(GlobalConstant.BUDGET_APPROVAL_CALLBACK_RELATION_KEY);
            String value = repeatValue == null ? IdGenerator.get() : repeatValue;
            callbackDatum.put(GlobalConstant.BUDGET_APPROVAL_CALLBACK_RELATION_KEY, value);
            actionContext.setAttribute(GlobalConstant.BUDGET_APPROVAL_CALLBACK_RELATION_KEY, value);
            this.updatedFieldMap.put(GlobalConstant.BUDGET_APPROVAL_CALLBACK_MAP_KEY, callbackDatum);
        } else {
            budgetService.buildCallbackKey(actionContext);
        }
        if (hasEditRewardRule) {
            this.updatedFieldMap.put(GlobalConstant.REWARD_UPDATE_DATA_KEY, this.arg.getObjectData().toObjectData().get(TPMActivityFields.REWARD_RULE_JSON, String.class));
        }
    }

    private void AddApprovalCallback(String key, Object value) {
        if (!MapUtils.isNullOrEmpty(this.updatedFieldMap)) {
            Map<String, Object> callbackDatum = (Map<String, Object>) this.updatedFieldMap.getOrDefault(GlobalConstant.BUDGET_APPROVAL_CALLBACK_MAP_KEY, new HashMap<>());
            callbackDatum.put(key, value);
            this.updatedFieldMap.put(GlobalConstant.BUDGET_APPROVAL_CALLBACK_MAP_KEY, callbackDatum);
        }
    }

    private void AddApprovalCallback(String key) {
        String value = actionContext.getAttribute(key);
        if (value != null && !MapUtils.isNullOrEmpty(this.updatedFieldMap)) {
            Map<String, Object> callbackDatum = (Map<String, Object>) this.updatedFieldMap.getOrDefault(GlobalConstant.BUDGET_APPROVAL_CALLBACK_MAP_KEY, new HashMap<>());
            callbackDatum.put(key, value);
            this.updatedFieldMap.put(GlobalConstant.BUDGET_APPROVAL_CALLBACK_MAP_KEY, callbackDatum);
        }
    }

    @Override
    protected Map<String, Map<String, Object>> buildCallbackDataForAddAction(IObjectData objectData, Long detailCreateTime) {
        Map<String, Map<String, Object>> callbackData = super.buildCallbackDataForAddAction(objectData, detailCreateTime);
        String relationValue = actionContext.getAttribute(GlobalConstant.BUDGET_APPROVAL_CALLBACK_RELATION_KEY);
        if (!Strings.isNullOrEmpty(relationValue)) {
            Map<String, Object> instanceMap = callbackData.getOrDefault(objectData.getId(), new HashMap<>());
            callbackData.put(objectData.getId(), instanceMap);
            Map<String, Object> callbackDatum = new HashMap<>();
            callbackDatum.put(GlobalConstant.BUDGET_APPROVAL_CALLBACK_RELATION_KEY, relationValue);
            instanceMap.put(GlobalConstant.BUDGET_APPROVAL_CALLBACK_MAP_KEY, callbackDatum);
        }
        return callbackData;
    }

    private void validateBudget(Arg arg) {

        String type = (String) arg.getObjectData().get("record_type");
        int tenantId = Integer.parseInt(actionContext.getTenantId());
        //开启预算表
        if (budgetService.isOpenBudge(tenantId)) {
            String nowBudgetId = arg.getObjectData().get(TPMActivityFields.BUDGET_TABLE) != null ? (String) arg.getObjectData().get(TPMActivityFields.BUDGET_TABLE) : "";
            //todo:try lock
            //budgetService.tryLockBudget(actionContext, nowBudgetId);

            IObjectData beforeActivity = serviceFacade.findObjectData(User.systemUser(actionContext.getTenantId()), arg.getObjectData().getId(), ApiNames.TPM_ACTIVITY_OBJ);
            //之前已经关联预算表
            String beforeBudgetId = beforeActivity.get(TPMActivityFields.BUDGET_TABLE) != null ? (String) beforeActivity.get(TPMActivityFields.BUDGET_TABLE) : "";
            if (!Strings.isNullOrEmpty(beforeBudgetId) && !beforeBudgetId.equals(nowBudgetId)) {
                throw new ValidateException(I18N.text(I18NKeys.ACTIVITY_RELATED_BUDGET_CAN_NOT_CHANGE_BUDGET));
            }
            String lifeStatus = (String) arg.getObjectData().get("life_status");
            if (!"ineffective".equals(lifeStatus) && Strings.isNullOrEmpty(beforeBudgetId) && !Strings.isNullOrEmpty(nowBudgetId)) {
                throw new ValidateException(I18N.text(I18NKeys.ACTIVITY_RELATED_A_NEW_BUDGET_IS_NOT_ALLOW));
            }
            //新关联的预算表需要先计算活动
            if (Strings.isNullOrEmpty(beforeBudgetId) && !beforeBudgetId.equals(nowBudgetId)) {
                actionContext.setAttribute(NEW_ADD_BUDGET, "yes");
            }

            if (!Strings.isNullOrEmpty(nowBudgetId)) {

                IObjectData budget = serviceFacade.findObjectData(User.systemUser(actionContext.getTenantId()), (String) arg.getObjectData().get(TPMActivityFields.BUDGET_TABLE), ApiNames.TPM_ACTIVITY_BUDGET);
                log.info("budget:{}", budget);
                if (!ObjectLifeStatus.NORMAL.getCode().equals(budget.get(CommonFields.LIFE_STATUS))) {
                    throw new ValidateException(I18N.text(I18NKeys.ACTIVITY_OBJ_EDIT_ACTION_20));
                }
                //判断时间
                Integer startMonth = budgetService.getBudgetStartMonth(tenantId);
                Long activityBeginTime = arg.getObjectData().toObjectData().get(TPMActivityFields.BEGIN_DATE, Long.class);
                int budgetYear = Integer.parseInt((String) budget.get(TPMActivityBudgetFields.PERIOD_YEAR));
                LocalDate budgetStartDate = LocalDate.of(budgetYear, startMonth, 1);
                LocalDate budgetEndDate = budgetStartDate.plusYears(1);
                LocalDate activityBeginDate = Instant.ofEpochMilli(activityBeginTime).atZone(ZoneOffset.ofHours(8)).toLocalDate();
                if (budgetStartDate.isAfter(activityBeginDate) || budgetEndDate.isBefore(activityBeginDate) || budgetEndDate.equals(activityBeginDate)) {
                    throw new ValidateException(I18N.text(I18NKeys.ADD_ACTIVITY_TIME_IS_NOT_FIT_BUDGET));
                }

                //department
                String budgetDepartment = ((List<String>) budget.get(TPMActivityBudgetFields.BUDGET_DEPARTMENT)).get(0);
                List<String> subDeptIds = serviceFacade.getSubDeptByDeptId(actionContext.getTenantId(), User.SUPPER_ADMIN_USER_ID, budgetDepartment, true);


                if (!CommonUtils.isIntersection(subDeptIds, activityDepartments)) {
                    throw new ValidateException(I18N.text(I18NKeys.ADD_ACTIVITY_BUDGET_DEPARTMENT_NOT_FIT));
                }

                double activityAmountInObj = arg.getObjectData().get(TPMActivityFields.ACTIVITY_AMOUNT) == null ? 0D : Double.parseDouble(arg.getObjectData().getOrDefault(TPMActivityFields.ACTIVITY_AMOUNT, "0").toString());
                if (activityAmountInObj < 0)
                    throw new ValidateException(I18N.text(I18NKeys.ACTIVITY_AMOUNT_CAN_NOT_BE_ZERO));
                //预算

                Map<String, Double> amountMap = new HashMap<>();
                double availableAmount = budgetService.getBudgetAvailableAmount(actionContext.getTenantId(), budget, amountMap);
                Double activityAmount = arg.getObjectData().get(TPMActivityFields.ACTIVITY_AMOUNT) == null ? 0D : Double.parseDouble(String.valueOf(arg.getObjectData().getOrDefault(TPMActivityFields.ACTIVITY_AMOUNT, "0")));

                LogData logData = LogData.builder().data(JSON.toJSONString(arg)).updateMap(this.updatedFieldMap).originalData(this.dbMasterData).build();
                logData.setAttribute("budget", budget);
                logData.setAttribute("amountMap", amountMap);
                String logId = null;
                IObjectData detail = null;
                //取消或则驳回后继续申请
                if ("under_review".equals(lifeStatus) || "ineffective".equals(lifeStatus)) {
                    if (!TPMGrayUtils.disableBudgetAmountJudge(actionContext.getTenantId()) && CommonUtils.keepNDecimal(availableAmount - activityAmount, 3) < 0) {
                        throw new ValidateException(I18N.text(I18NKeys.ADD_ACTIVITY_BUDGET_INSUFFICIENT));
                    }
                    boolean needTriggerApproval = budgetService.needApproval(actionContext.getTenantId(), ApiNames.TPM_ACTIVITY_OBJ, arg.getObjectData().getId(), "Create");
                    actionContext.setAttribute("needTriggerApproval", needTriggerApproval);

                    logId = operateInfoService.log(actionContext.getTenantId(), LogType.ADD.value(), JSON.toJSONString(logData), actionContext.getUser().getUpstreamOwnerIdOrUserId(), ApiNames.TPM_ACTIVITY_OBJ, arg.getObjectData().getId(), needTriggerApproval);
                    detail = budgetService.addBudgetDetail(actionContext.getTenantId(), actionContext.getUser().getUpstreamOwnerIdOrUserId(),
                            "1",
                            budget.getId(),
                            String.format("活动新建：「%s」申请", arg.getObjectData().get("name")),//ignorei18n
                            -activityAmount,
                            availableAmount,
                            availableAmount - activityAmount,
                            System.currentTimeMillis(),
                            String.format("LogId:%s-traceId:%s", logId, TraceContext.get().getTraceId()),
                            arg.getObjectData().getId(),
                            TraceContext.get().getTraceId(),
                            IdempotentArgBase.builder().idempotentKey(actionContext.getPostId() + ":" + arg.getObjectData().getId()).build());
                } else {
                    boolean needTriggerApproval = !MapUtils.isNullOrEmpty(this.updatedFieldMap) && budgetService.needUpdateApproval(actionContext.getTenantId(), ApiNames.TPM_ACTIVITY_OBJ, arg.getObjectData().getId(), "Update", this.updatedFieldMap);
                    actionContext.setAttribute("needTriggerApproval", needTriggerApproval);

                    Double beforeActivityAmount = beforeActivity.get(TPMActivityFields.ACTIVITY_AMOUNT) == null ? 0D : Double.parseDouble(beforeActivity.get(TPMActivityFields.ACTIVITY_AMOUNT, String.class, "0"));
                    if (!TPMGrayUtils.disableBudgetAmountJudge(actionContext.getTenantId()) && !beforeActivityAmount.equals(activityAmount) && availableAmount + beforeActivityAmount - activityAmount < 0) {
                        throw new ValidateException(I18N.text(I18NKeys.ADD_ACTIVITY_BUDGET_INSUFFICIENT));
                    }
                    if (beforeActivityAmount.equals(activityAmount)) {
                        log.info("activity has the same amount. before:{},now:{}", beforeActivityAmount, activityAmount);
                        return;
                    }
                    JSONObject snapshot = budgetService.getApprovalInstanceSnapshot(actionContext.getTenantId(), ApiNames.TPM_ACTIVITY_OBJ, arg.getObjectData().getId());
                    logData.setAttribute("snapshot", snapshot);

                    double incrementAmount = activityAmount - beforeActivityAmount;
                    if (incrementAmount > 0 || (!needTriggerApproval && CommonUtils.keepNDecimal(incrementAmount, 3) != 0)) {
                        logId = operateInfoService.log(actionContext.getTenantId(), LogType.EDIT.value(), JSON.toJSONString(logData), actionContext.getUser().getUpstreamOwnerIdOrUserId(), ApiNames.TPM_ACTIVITY_OBJ, arg.getObjectData().getId(), needTriggerApproval);
                        detail = budgetService.addBudgetDetail(actionContext.getTenantId(), actionContext.getUser().getUpstreamOwnerIdOrUserId(),
                                incrementAmount < 0 ? "2" : "1",
                                budget.getId(),
                                String.format("个案活动编辑：「%s」%s活动预算", arg.getObjectData().get("name"), (incrementAmount < 0 ? "追加" : "削减")),//ignorei18n
                                -incrementAmount,
                                availableAmount,
                                availableAmount - incrementAmount,
                                System.currentTimeMillis(),
                                String.format("LogId:%s-traceId:%s", logId, TraceContext.get().getTraceId()),
                                arg.getObjectData().getId(),
                                TraceContext.get().getTraceId(),
                                IdempotentArgBase.builder().idempotentKey(actionContext.getPostId() + ":" + arg.getObjectData().getId()).build());
                    } else {
                        actionContext.setAttribute(GlobalConstant.BUDGET_APPROVAL_CALLBACK_INCREASE_AMOUNT_KEY, incrementAmount);
                        AddApprovalCallback(GlobalConstant.BUDGET_APPROVAL_CALLBACK_INCREASE_AMOUNT_KEY, incrementAmount);
                        log.info("is a add amount action to budget.updateMap:{}", this.updatedFieldMap);
                    }
                }
                if (detail != null)
                    actionContext.setAttribute("detail_id", detail.getId());
                if (logId != null)
                    actionContext.setAttribute("operate_log_id", logId);


                //calculate budget
                List<String> updateBudgetIds = new ArrayList<>();
                if (!nowBudgetId.equals(beforeBudgetId)) {
                    budgetService.calculateBudget(actionContext.getTenantId(), (String) beforeActivity.get(TPMActivityFields.BUDGET_TABLE));
                    updateBudgetIds.add(budget.getId());
                    if (!Strings.isNullOrEmpty(beforeBudgetId))
                        updateBudgetIds.add(beforeBudgetId);
                } else {
                    //just edit activity amount.should calculate
                    if (!activityAmount.equals(beforeActivity.get(TPMActivityFields.ACTIVITY_AMOUNT))) {
                        updateBudgetIds.add(budget.getId());
                    }
                }
                actionContext.setAttribute(String.format(SEND_BUDGET_IDS, actionContext.getPostId()), updateBudgetIds);
            }
        }
    }

    @Override
    protected Result doAct(Arg arg) {
        releaseProofEnableCacheOfActivityObj(this.dbMasterData);
        tryLock();
        return packTransactionProxy.packAct(this, arg);
    }

    private void validateActivityInUse(Arg arg) {
        if (hasDataWithActivityId(actionContext.getTenantId(), ApiNames.TPM_ACTIVITY_PROOF_OBJ, TPMActivityProofFields.ACTIVITY_ID, arg.getObjectData().getId())) {
            throw new ValidateException(I18N.text(I18NKeys.ACTIVITY_WHICH_HAS_RELATED_BY_PROOF_CAN_NOT_EDIT));
        }

        if (this.activityTypeExt != null && this.activityTypeExt.writeOffNode() != null) {
            String activityApiName = this.activityTypeExt.writeOffNode().getReferenceActivityFieldApiName();
            if (hasDataWithActivityId(actionContext.getTenantId(), ApiNames.TPM_DEALER_ACTIVITY_COST, activityApiName, arg.getObjectData().getId())) {
                throw new ValidateException(I18N.text(I18NKeys.ACTIVITY_OBJ_EDIT_ACTION_21));
            }
        }
    }

    private boolean hasDataWithActivityId(String tenantId, String objectApiName, String activityFieldApiName, String activityId) {
        SearchTemplateQuery query = new SearchTemplateQuery();

        query.setLimit(1);
        query.setOffset(0);
        query.setSearchSource("db");

        Filter activityFilter = new Filter();
        activityFilter.setFieldName(activityFieldApiName);
        activityFilter.setOperator(Operator.EQ);
        activityFilter.setFieldValues(Lists.newArrayList(activityId));

        query.setFilters(Lists.newArrayList(activityFilter));

        return serviceFacade.countObjectDataFromDB(tenantId, objectApiName, query) > 0;
    }

    private void validateActivityStatus(Arg arg) {

        long begin = arg.getObjectData().toObjectData().get(TPMActivityFields.BEGIN_DATE, Long.class);
        long end = arg.getObjectData().toObjectData().get(TPMActivityFields.END_DATE, Long.class);
        long now = System.currentTimeMillis();
        String status;
        if (now < begin) {
            status = TPMActivityFields.ACTIVITY_STATUS__SCHEDULE;
        } else if (now < end) {
            status = TPMActivityFields.ACTIVITY_STATUS__IN_PROGRESS;
        } else {
            status = TPMActivityFields.ACTIVITY_STATUS__END;
        }
        if (TPMGrayUtils.isLjjUpdateStatusByCloseDate(actionContext.getTenantId())) {
            status = calculateActivityStatusByCloseData(arg, now, status);
        }

        arg.getObjectData().put(TPMActivityFields.ACTIVITY_STATUS, status);
    }

    private String calculateActivityStatusByCloseData(Arg arg, long now, String status) {
        // 亮晶晶 自定义字段，取消活动执行时间
        Long closeDate = (Long) arg.getObjectData().get("field_f1sJK__c");
        if (closeDate != null && now > closeDate) {
            status = TPMActivityFields.ACTIVITY_STATUS__CLOSED;
        }
        return status;
    }

    private void validateDateRange(Arg arg) {

        // 不启用，则默认值
        if (!this.enableActivityCycleControl) {
            supplyActivityDate(arg);
        }
        IObjectData data = arg.getObjectData().toObjectData();
        if (data.get(TPMActivityFields.BEGIN_DATE) == null || data.get(TPMActivityFields.END_DATE) == null) {
            throw new ValidateException(I18N.text(I18NKeys.ACTIVITY_OBJ_EDIT_ACTION_22));
        }

        long begin = data.get(TPMActivityFields.BEGIN_DATE, Long.class);
        Long endDate = data.get(TPMActivityFields.END_DATE, Long.class);
        long end = TimeUtils.convertToDayEndIfTimeWasDayBegin(endDate);
        arg.getObjectData().put(TPMActivityFields.END_DATE, end);
        if (endDate != end) {
            this.updatedFieldMap.put(TPMActivityFields.END_DATE, end);
        }

        if (end <= begin) {
            throw new ValidateException(I18N.text(I18NKeys.ADD_ACTIVITY_DATE_ERROR));
        }

        unifiedActivityCommonLogicBusiness.checkTimeRangeIsInTheRangeOfUnifiedActivityLimits(unifiedActivity, begin, end);

        log.info("end date : {}", end);
    }

    private void supplyActivityDate(Arg arg) {
        if (arg.getObjectData().get(TPMActivityFields.ACTIVITY_UNIFIED_CASE_ID) != null) {
            Long begin = arg.getObjectData().toObjectData().get(TPMActivityFields.BEGIN_DATE, Long.class);
            Long end = arg.getObjectData().toObjectData().get(TPMActivityFields.END_DATE, Long.class);
            if (begin != null && begin.equals(TimeUtils.MIN_DATE)) {
                arg.getObjectData().put(TPMActivityFields.BEGIN_DATE, unifiedActivity.get(TPMActivityUnifiedCaseFields.START_DATE, Long.class));
                if (this.updatedFieldMap.containsKey(TPMActivityFields.BEGIN_DATE)) {
                    this.updatedFieldMap.put(TPMActivityFields.BEGIN_DATE, unifiedActivity.get(TPMActivityUnifiedCaseFields.START_DATE, Long.class));
                }
            }
            if (end != null && end.equals(TimeUtils.MAX_DATE)) {
                arg.getObjectData().put(TPMActivityFields.END_DATE, unifiedActivity.get(TPMActivityUnifiedCaseFields.END_DATE, Long.class));
                if (this.updatedFieldMap.containsKey(TPMActivityFields.END_DATE)) {
                    this.updatedFieldMap.put(TPMActivityFields.END_DATE, unifiedActivity.get(TPMActivityUnifiedCaseFields.END_DATE, Long.class));
                }
            }
        } else {
            arg.getObjectData().putIfAbsent(TPMActivityFields.BEGIN_DATE, TimeUtils.MIN_DATE);
            if (this.updatedFieldMap.containsKey(TPMActivityFields.BEGIN_DATE)) {
                this.updatedFieldMap.putIfAbsent(TPMActivityFields.BEGIN_DATE, TimeUtils.MIN_DATE);
            }
            arg.getObjectData().putIfAbsent(TPMActivityFields.END_DATE, TimeUtils.MAX_DATE);
            if (this.updatedFieldMap.containsKey(TPMActivityFields.END_DATE)) {
                this.updatedFieldMap.putIfAbsent(TPMActivityFields.END_DATE, TimeUtils.MAX_DATE);
            }
        }
        if (Objects.equals(arg.getObjectData().get(TPMActivityFields.BEGIN_DATE), 0)) {
            arg.getObjectData().put(TPMActivityFields.BEGIN_DATE, TimeUtils.MIN_DATE);
        }
    }

    private void validateEnableEdit(Arg arg) {

        //各企业灰度的可编辑字段校验
        if (!onlyChangeDetailAllowedField()) {
            //是否执行活动
            activityService.validateActivityEnableEdit(actionContext.getTenantId(), arg.getObjectData().getId());
        }
    }

    @Override
    protected Result after(Arg arg, Result result) {
        Result afterRst = super.after(arg, result);
        //编辑奖励规则
        editRewardRule();
        //有审批没有用
        List<String> updateBudgetIds = actionContext.getAttribute(String.format(SEND_BUDGET_IDS, actionContext.getPostId()));
        log.info("updateBudgetIds:{}", updateBudgetIds);
        if (!CollectionUtils.isEmpty(updateBudgetIds)) {
            boolean isCalculateActivity = !Strings.isNullOrEmpty(actionContext.getAttribute(NEW_ADD_BUDGET));
            updateBudgetIds.forEach(id -> {
                if (isCalculateActivity) {
                    budgetService.calculateActivity(actionContext.getTenantId(), arg.getObjectData().getId());
                }
                budgetService.calculateBudget(actionContext.getTenantId(), id);
            });
        }
        resetActivityStatus(result);
        recalculateAmount(result.getObjectData().toObjectData());
        releaseProofEnableCacheOfActivityObj(result.getObjectData().toObjectData());
        // 促销类 新增促销规则
        updatePromotionPolicy(afterRst.getObjectData().toObjectData(), PRODUCT_GIFT_DATA);
        return afterRst;
    }

    private void releaseProofEnableCacheOfActivityObj(IObjectData activity) {
        if (!tpm2Service.isTPM2Tenant(Integer.valueOf(actionContext.getTenantId()))) {
            return;
        }
        if (TPMGrayUtils.skipEnableCheck(actionContext.getTenantId())) {
            return;
        }
        try {
            List<String> list = Lists.newArrayList(TPMActivityFields.STORE_RANGE, TPMActivityFields.MULTI_DEPARTMENT_RANGE, TPMActivityFields.DEALER_ID, TPMActivityFields.ACTIVITY_UNIFIED_CASE_ID, TPMActivityFields.CUSTOMER_TYPE);
            if (!isApprovalFlowStartSuccess(activity.getId()) && list.stream().anyMatch(updatedFieldMap::containsKey)) {
                ParallelUtils.createParallelTask().submit(MonitorTaskWrapper.wrap(() -> enableCacheService.resetCacheByActivity(actionContext.getTenantId(), activity))).run();
            }
        } catch (Exception e) {
            log.info("releaseProofEnableCacheOfActivityObj error: ", e);
        }
    }

    private void recalculateAmount(IObjectData data) {
        if (!this.isApprovalFlowStartSuccess(data.getId())) {
            unifiedActivityCommonLogicBusiness.recalculateUnifiedAmountField(actionContext.getTenantId(), data.get(TPMActivityFields.ACTIVITY_UNIFIED_CASE_ID, String.class));
        }
    }

    private void updateApprovalIdToDetail(Result result) {
        boolean needTriggerApproval = Boolean.TRUE.equals(actionContext.getAttribute("needTriggerApproval"));
        if (needTriggerApproval) {
            if (!this.isApprovalNotExist() && actionContext.getAttribute("operate_log_id") != null) {
                String approvalId = actionContext.getAttribute(GlobalConstant.BUDGET_APPROVAL_CALLBACK_RELATION_KEY);
                operateInfoService.updateLog(actionContext.getAttribute("operate_log_id"), actionContext.getTenantId(), approvalId);
                if (actionContext.getAttribute("detail_id") != null)
                    budgetService.updateApprovalIdForDetail(actionContext.getTenantId(), actionContext.getAttribute("detail_id"), approvalId);
            }
        }
    }


    @Override
    protected void finallyDo() {
        try {
            super.finallyDo();
        } finally {
            budgetService.unLockBudget(actionContext);
        }
    }

    @Override
    public Result doActTransaction(Arg arg) {
        validateBudget(arg);
        Result result = super.doAct(arg);
        updateApprovalIdToDetail(result);
        handlerPersonnelRewardRule(result);
        editProofPeriodTime(result);
        return result;
    }

    private void editProofPeriodTime(Result result) {
        tpmDisplayReportService.editProofPeriodTime(actionContext.getUser().getUpstreamOwnerIdOrUserId(), result);
    }

    //1、拼规则
    //2、获取code，存code
    private void handlerPersonnelRewardRule(Result result) {
        IObjectData data = result.getObjectData().toObjectData();
        String where = data.get(TPMActivityFields.PERSON_REWARD_RULE_WHERE, String.class);
        String apiName = data.get("action_rule_object__c", String.class);
        if (StringUtils.isEmpty(apiName)) {
            return;
        }
        String ruleTemplateTenant = data.get("action_rule_template_tenant__c", String.class);
        String ruleTemplateTenantId = TEMPLATE_TENANT_ID.get(ruleTemplateTenant);
        if (StringUtils.isEmpty(ruleTemplateTenantId)) {
            throw new ValidateException("action_rule_template_tenant__c not found");
        }
        String ruleTemplateTenantBefore = activityInDB.get("action_rule_template_tenant__c", String.class);
        String conditionCode = data.get(TPMActivityFields.PERSON_REWARD_RULE_CODE, String.class);
        if (!Objects.equals(ruleTemplateTenant, ruleTemplateTenantBefore)) {
            conditionCode = null;
        }
        String conditionCodeUpdater = personnelRewardRuleService.buildConditionCode(ruleTemplateTenantId, -10000, apiName, conditionCode, JSON.parseArray(where, PersonRewardRuleWhereConditionVO.class));
        Map<String, Object> updateMap = new HashMap<>();
        updateMap.put(TPMActivityFields.PERSON_REWARD_RULE_CODE, conditionCodeUpdater);
        serviceFacade.updateWithMap(User.systemUser(actionContext.getTenantId()), result.getObjectData().toObjectData(), updateMap);
    }


    private void updatePromotionPolicy(IObjectData objectData, String productGiftData) {
        if (Objects.nonNull(ACTIVITY_TYPE_TEMPLATE_ID) && ACTIVITY_TYPE_TEMPLATE_ID.startsWith("promotion")) {
            //判断是否需要更新价格政策明细限量
            boolean isUpdateLimitAccount = getIsUpdateLimitAccount();
            saveOrUpdatePromotionPolicy(objectData, productGiftData, isUpdateLimitAccount);

            log.info("activity life status is {}", objectData.get(CommonFields.LIFE_STATUS));
            if (Objects.equals(objectData.get(CommonFields.LIFE_STATUS, String.class), CommonFields.LIFE_STATUS__NORMAL)) {
                updateSFAPromotionPolicy(objectData);
            } else {
                promotionPolicyService.disablePromotionPolicy(actionContext.getTenantId(), actionContext.getUser().getUpstreamOwnerIdOrUserId(), objectData);
            }
        }
    }

    private boolean getIsUpdateLimitAccount() {
        boolean isUpdateLimitAccount = false;
        if (this.detailChangeMap.size() > 0 && detailChangeMap.containsKey(ApiNames.TPM_ACTIVITY_STORE_OBJ)) {
            isUpdateLimitAccount = true;
        }
        if (!isUpdateLimitAccount) {
            if (this.updatedFieldMap.size() > 0) {
                List<String> updates = Lists.newArrayList(TPMActivityFields.ACTIVITY_AMOUNT, TPMActivityFields.LIMIT_OBJ_TYPE,
                        TPMActivityFields.STORE_RANGE, TPMActivityFields.MODE_TYPE, TPMActivityFields.ACCOUNT_UNIFY_LIMIT_AMOUNT);
                List<String> updateFields = new ArrayList<>(this.updatedFieldMap.keySet());
                if (CollectionUtils.containsAny(updateFields, updates)) {
                    isUpdateLimitAccount = true;
                }
            }
        }
        return isUpdateLimitAccount;
    }

    private void updateSFAPromotionPolicy(IObjectData objectData) {
        promotionPolicyService.updateSFAPromotionPolicy(actionContext.getTenantId(),
                actionContext.getUser().getUpstreamOwnerIdOrUserId(),
                objectData);
    }

    private void saveOrUpdatePromotionPolicy(IObjectData objectData, String productGiftData, Boolean isUpdateLimitAccount) {
        promotionPolicyService.saveOrUpdate(actionContext.getTenantId(),
                actionContext.getUser().getUpstreamOwnerIdOrUserId(),
                objectData,
                productGiftData,
                isUpdateLimitAccount);
    }

    private void sendActivityObjAuditLog(Arg arg) {
        crmAuditLogService.sendLog(CrmAuditLogDTO.builder()
                .tenantId(actionContext.getTenantId())
                .userId(String.valueOf(User.systemUser(actionContext.getTenantId())))
                .action("Edit")
                .objectApiNames(ApiNames.TPM_ACTIVITY_OBJ)
                .message("编辑活动申请")//ignorei18n
                .parameters(JSONObject.toJSONString(arg))
                .build());
    }

    private void resetActivityStatus(Result result) {
        if (tpm2Service.isTPM2Tenant(Integer.valueOf(actionContext.getTenantId()))) {
            String lifeStatus = (String) result.getObjectData().get(CommonFields.LIFE_STATUS);
            if (!ObjectLifeStatus.NORMAL.getCode().equals(lifeStatus)) {
                Map<String, Object> updateMap = new HashMap<>();
                updateMap.put(TPMActivityFields.ACTIVITY_STATUS, TPMActivityFields.ACTIVITY_STATUS__APPROVAL);
                serviceFacade.updateWithMap(User.systemUser(actionContext.getTenantId()), result.getObjectData().toObjectData(), updateMap);
            }
        }
    }


    private void tryLock() {
        if (budgetService.isOpenBudge(Integer.parseInt(actionContext.getTenantId()))) {
            String nowBudgetId = arg.getObjectData().get(TPMActivityFields.BUDGET_TABLE) != null ? (String) arg.getObjectData().get(TPMActivityFields.BUDGET_TABLE) : "";
            //todo:try lock
            budgetService.tryLockBudget(actionContext, nowBudgetId);
        }
    }

    private boolean onlyChangeDetailAllowedField() {
        if (TPMGrayUtils.isYinLu(actionContext.getTenantId())) {
            return onlyChangeDetailAllowedFieldForYL();
        }
        List<String> tpmAllowedEditFields = tpmAllowEditFieldsService.getTPMAllowedEditFields();

        List<String> preFields = this.needUpdatedFieldMap.keySet().stream().filter(s ->
                !s.contains("CALLBACK") && !s.contains("KEY") && !s.endsWith("__c") && !tpmAllowedEditFields.contains(s)
        ).collect(Collectors.toList());

        Set<String> all = ALLOW_EDIT_FIELD_MAP.getOrDefault("all", new HashSet<>());
        if (!CollectionUtils.isEmpty(all) && all.contains(actionContext.getTenantId())) {
            return true;
        }

        if (!CollectionUtils.isEmpty(preFields)) {
            HashSet<Object> allowEditFields = new HashSet<>();
            allowEditFields.addAll(ALLOW_EDIT_FIELD_MAP.getOrDefault("default", new HashSet<>()));
            allowEditFields.addAll(ALLOW_EDIT_FIELD_MAP.getOrDefault(actionContext.getTenantId(), new HashSet<>()));
            if (!CollectionUtils.isEmpty(allowEditFields)) {
                preFields = preFields.stream().filter(s -> !allowEditFields.contains(s)).collect(Collectors.toList());
            }
        }

        log.info("TPMActivityObjEdit validateEnableEdit preFields : {}", preFields);
        if (preFields.size() > 0) {
            return false;
        }
        Set<String> allowedDetailField = ALLOW_EDIT_DETAIL_FIELD_MAP.getOrDefault(actionContext.getTenantId(), new HashSet<>());
        Map<String, Map<String, Object>> innerDetailChangeMap = new HashMap<>(this.detailChangeMap);

        if (TPMGrayUtils.isOpenCustomObjectEdit(actionContext.getTenantId())) {
            List<String> customObjectApiNames = innerDetailChangeMap.keySet().stream().filter(s -> s.contains("__c")).collect(Collectors.toList());
            customObjectApiNames.forEach(innerDetailChangeMap::remove);
            if (CollectionUtils.isEmpty(innerDetailChangeMap)) {
                return true;
            }
        }

        for (Map.Entry<String, Map<String, Object>> entry : innerDetailChangeMap.entrySet()) {
            Map<String, Object> modeMap = entry.getValue();
            for (Map.Entry<String, Object> e : modeMap.entrySet()) {
                String mode = e.getKey();
                if (!ObjectAction.UPDATE.getActionCode().equals(mode)) {
                    return true;
                }
                Object id2Value = e.getValue();
                Map<String, ObjectDataDocument> entity = (Map<String, ObjectDataDocument>) id2Value;
                if (entity.values().stream().anyMatch(v -> v.keySet().stream().anyMatch(key ->
                        !key.endsWith("__c") && !allowedDetailField.contains(key) && !tpmAllowedEditFields.contains(key)
                ))) {
                    return false;
                }
            }
        }
        return true;
    }

    private boolean onlyChangeDetailAllowedFieldForYL() {

        Set<String> masterKeySet = new HashSet<>(this.updatedFieldMap.keySet());
        masterKeySet.remove(GlobalConstant.BUDGET_APPROVAL_CALLBACK_MAP_KEY);
        masterKeySet.remove(GlobalConstant.REWARD_UPDATE_DATA_KEY);
        // 灰度跳过是否可编辑校验
        if (TPMGrayUtils.skipValidateActivityDepartmentRange(actionContext.getTenantId())) {
            masterKeySet.remove(TPMActivityFields.MULTI_DEPARTMENT_RANGE);
        }
        if (TPMGrayUtils.allowEditActivityPreField(actionContext.getTenantId())) {
            masterKeySet.remove(TPMActivityFields.ACTIVITY_AMOUNT);
            masterKeySet.remove(TPMActivityFields.END_DATE);
            masterKeySet.remove(TPMActivityFields.REMAINING_WRITE_OFF_AMOUNT);
        }
        log.info("updateFieldMap keys is {}", masterKeySet);
        if (masterKeySet.size() > 0) {
            return false;
        }
        IObjectData activity = serviceFacade.findObjectData(User.systemUser(actionContext.getTenantId()), arg.getObjectData().getId(), ApiNames.TPM_ACTIVITY_OBJ);
        Map<String, List<IObjectData>> originalDetails = getDetailsMap(activity, User.systemUser(actionContext.getTenantId()));
        validateDetailChange(arg.getDetails(), originalDetails);
        return true;
    }

    private void validateDetailChange(Map<String, List<ObjectDataDocument>> details,
                                      Map<String, List<IObjectData>> originalDetails) {

        List<IObjectData> oldProductRangeObjects = originalDetails.get(ApiNames.TPM_ACTIVITY_UNIFIED_CASE_PRODUCT_RANGE_OBJ);
        List<ObjectDataDocument> currentProductRangeObjects = details.getOrDefault(ApiNames.TPM_ACTIVITY_UNIFIED_CASE_PRODUCT_RANGE_OBJ, new ArrayList<>());
        if (org.apache.commons.collections.CollectionUtils.isNotEmpty(oldProductRangeObjects)) {
            List<String> oldProductIds = oldProductRangeObjects.stream().map(v -> v.get("product_id", String.class)).collect(Collectors.toList());
            List<String> currentProductIds = currentProductRangeObjects.stream().map(v -> (String) v.get("product_id")).collect(Collectors.toList());

            if (!new HashSet<>(currentProductIds).containsAll(oldProductIds)) {
                throw new ValidateException(I18N.text(I18NKeys.ACTIVITY_OBJ_EDIT_ACTION_23));
            }
        }
    }

    private Map<String, List<IObjectData>> getDetailsMap(IObjectData masterData, User user) {

        List<IObjectDescribe> detailDescribe = serviceFacade.findDetailDescribes(user.getTenantId(), masterData.getDescribeApiName());
        return serviceFacade.findDetailObjectDataList(detailDescribe, masterData, user);
    }

    private void validateRewardRule() {
        if (rewardRuleData != null) {
            try {
                rewardRuleData.setRuleType(activityTypeExt.get().getTemplateId());
                ActivityRewardRulePO validatePO = rewardRuleData.toPO();
                if (rewardRuleManager.checkHasEdit(validatePO)) {
                    rewardRuleManager.validateRule(rewardRuleData);
                    hasEditRewardRule = true;
                }
                validatePO.getRewardDetails().forEach(detail ->
                {
                    if (Objects.nonNull(detail.getRewardStrategy())) {
                        detail.getRewardStrategy().setValidate(false);
                    }
                });
                if (rewardRuleManager.checkHasEdit(validatePO)) {
                    rewardRuleManager.validateContainsFailRewardDetail(actionContext.getTenantId(), rewardRuleData.getUniqueId());
                }
                RewardDetailDTO consumerRewardDetail = rewardRuleData.getRewardDetails().get(rewardRuleData.getRewardDetails().size() - 1);
                if (consumerRewardDetail.getRewardNode().getRewardDimension().equals(RewardDimensionEnum.CONSUMER.code()) && Objects.nonNull(consumerRewardDetail.getRewardStrategy())) {
                    if (consumerRewardDetail.getRewardStrategy().getRewardMethod().equals(RewardMethodEnum.PHYSICAL_ITEM.code())) {
                        String accountOption = objectData.get(TPMActivityFields.PHYSICAL_ITEM_WRITE_OFF_CLOUD_ACCOUNT, String.class);
                        if (Strings.isNullOrEmpty(accountOption) && describeCacheService.isExistField(actionContext.getTenantId(), ApiNames.TPM_ACTIVITY_OBJ, TPMActivityFields.PHYSICAL_ITEM_WRITE_OFF_CLOUD_ACCOUNT)) {
                            throw new ValidateException(I18N.text(I18NKeys.VALIDATE_ACTIVITY_OBJ_EDIT_ACTION_0));
                        }
                        if (CollectionUtils.isEmpty(this.arg.getDetails().get(ApiNames.TPM_ACTIVITY_PRIZES_OBJ))) {
                            throw new ValidateException(I18N.text(I18NEnums.PLEASE_FILL_ACTIVITY_PRIZE_DETAIL.getCode()));
                        }
                    } else if (consumerRewardDetail.getRewardStrategy().getRewardMethod().equals(RewardMethodEnum.RED_PACKET.code())) {
                        Map<String, Object> redPacketMap = rewardRuleManager.getConsumerRedPacketSettingInfo(consumerRewardDetail);
                        redPacketMap.forEach(objectData::set);
                        this.updatedFieldMap.putAll(redPacketMap);
                    }

                }
            } catch (BizException exception) {
                throw new ValidateException(exception.getMessage());
            }
        }
    }

    private void editRewardRule() {
        if (!isMasterApprovalStartSuccess() && rewardRuleData != null) {
            try {
                rewardRuleData.setRuleType(activityTypeExt.get().getTemplateId());
                activityRewardRuleService.update(UpdateRewardRule.Arg.builder().tenantId(actionContext.getTenantId()).rewardRule(rewardRuleData).build());
            } catch (BizException exception) {
                throw new ValidateException(exception.getMessage());
            }
        }
    }

    private void validateProductRange() {
        // 是否囤货激励业务类型 客开 sign_in_goods_rewards__c
        String recordType = objectData.getRecordType();
        if (rewardRuleData != null || TPMActivityFields.ActivityRecordType.SIGN_IN_GOODS_REWARDS.equals(recordType)
                && TPMGrayUtils.isMengNiuSignInGoodsFreshStandard(actionContext.getTenantId())) {
            String rangOp = objectData.get(TPMActivityFields.PRODUCT_RANGE_FRESH_STANDARD, String.class);
            if (Strings.isNullOrEmpty(rangOp)) {
                throw new ValidateException(I18N.text(I18NEnums.FRESHNESS_MATCHING_3.getCode()));
            }
            boolean isBigDate = ScanCodeActionConstants.BIG_DATE_ACTIVITY_TYPE_TEMPLATE_ID.equals(ACTIVITY_TYPE_TEMPLATE_ID);
            //非”不限制“和大日期都得有从对象
            if ((!TPMActivityFields.ProductRangeFreshStandard.NO_LIMIT.equals(rangOp) || isBigDate) &&
                    (!this.detailObjectData.containsKey(ApiNames.TPM_ACTIVITY_PRODUCT_RANGE_OBJ) || org.apache.commons.collections4.CollectionUtils.isEmpty(this.detailObjectData.get(ApiNames.TPM_ACTIVITY_PRODUCT_RANGE_OBJ)))) {
                throw new ValidateException(I18N.text(I18NKeys.ACTIVITY_OBJ_ADD_ACTION_19));
            }
            if (TPMActivityFields.ProductRangeFreshStandard.NO_LIMIT.equals(rangOp)) {
                return;
            }
            Set<String> productIdSet = new HashSet<>();
            Map<String, BigDecimal> repeatMap = new HashMap<>();
            this.detailObjectData.get(ApiNames.TPM_ACTIVITY_PRODUCT_RANGE_OBJ).forEach(product -> {
                String productId = product.get(TPMActivityProductRangeFields.PRODUCT_ID, String.class);
                BigDecimal consumerRetailPrice = product.get(TPMActivityProductRangeFields.CONSUMER_RETAIL_PRICE, BigDecimal.class, BigDecimal.ZERO).setScale(2, RoundingMode.DOWN);
                BigDecimal activityDeductAmount = product.get(TPMActivityProductRangeFields.ACTIVITY_DEDUCT_AMOUNT, BigDecimal.class, BigDecimal.ZERO).setScale(2, RoundingMode.DOWN);
                BigDecimal toBeExpiredDays = product.get(TPMActivityProductRangeFields.TO_BE_EXPIRED_DAYS, BigDecimal.class, BigDecimal.ZERO).setScale(2, RoundingMode.DOWN);
                Long manufactureBeginTime = product.get(TPMActivityProductRangeFields.MANUFACTURE_DATE_START, Long.class, 0L);
                Long manufactureEndTime = product.get(TPMActivityProductRangeFields.MANUFACTURE_DATE_END, Long.class, 0L);
                String matchType = product.get(TPMActivityProductRangeFields.MATCH_METHOD, String.class);
                if (manufactureEndTime != 0) {
                    manufactureEndTime = TimeUtils.convertToDayEnd(manufactureEndTime);
                    product.set(TPMActivityProductRangeFields.MANUFACTURE_DATE_END, manufactureEndTime);
                }

                if (TPMGrayUtils.disallowDuplicateProductRange(actionContext.getTenantId())) {
                    if (productIdSet.contains(productId)) {
                        throw new ValidateException(I18N.text(I18NKeys.ACTIVITY_OBJ_ADD_ACTION_20));
                    }
                    productIdSet.add(productId);
                } else if (isBigDate) {
                    BigDecimal consumerPrice = repeatMap.get(productId);
                    if (consumerPrice == null) {
                        repeatMap.put(productId, consumerRetailPrice);
                    } else {
                        if (consumerPrice.compareTo(consumerRetailPrice) != 0) {
                            throw new ValidateException(I18N.text(I18NKeys.ACTIVITY_OBJ_ADD_ACTION_21));
                        }
                        String key = String.format("%s.%s.%s", consumerRetailPrice, activityDeductAmount, toBeExpiredDays);
                        if (repeatMap.containsKey(key)) {
                            throw new ValidateException(I18N.text(I18NKeys.ACTIVITY_OBJ_ADD_ACTION_22));
                        }
                        repeatMap.put(key, BigDecimal.ZERO);
                    }
                }
                if (isBigDate) {
                    if (consumerRetailPrice.compareTo(BigDecimal.ZERO) <= 0) {
                        throw new ValidateException(I18N.text(I18NKeys.ACTIVITY_OBJ_ADD_ACTION_23));
                    }
                    product.set(TPMActivityProductRangeFields.CONSUMER_RETAIL_PRICE, consumerRetailPrice.setScale(2, RoundingMode.DOWN));
                    if (activityDeductAmount.compareTo(BigDecimal.ZERO) <= 0) {
                        throw new ValidateException(I18N.text(I18NKeys.ACTIVITY_OBJ_ADD_ACTION_24));
                    }
                    product.set(TPMActivityProductRangeFields.ACTIVITY_DEDUCT_AMOUNT, activityDeductAmount.setScale(2, RoundingMode.DOWN));
                    if (consumerRetailPrice.compareTo(activityDeductAmount) <= 0) {
                        throw new ValidateException(I18N.text(I18NKeys.ACTIVITY_OBJ_ADD_ACTION_26));
                    }
                }

                if (TPMActivityFields.ProductRangeFreshStandard.BY_REMAINING_DAYS.equals(rangOp)) {
                    if (toBeExpiredDays.compareTo(BigDecimal.ZERO) <= 0) {
                        throw new ValidateException(I18N.text(I18NKeys.ACTIVITY_OBJ_ADD_ACTION_25));
                    } else if (manufactureBeginTime > 0 || manufactureEndTime > 0) {
                        throw new ValidateException(I18N.text(I18NEnums.FRESHNESS_MATCHING_1.getCode()));
                    } else if (Strings.isNullOrEmpty(matchType)) {
                        throw new ValidateException(I18N.text("fmcg.crm.fmcg.tpm.ACTIVITY_OBJ_reward_match_method_is_empty"));
                    }
                }

                if (TPMActivityFields.ProductRangeFreshStandard.BY_DATE_RANGE.equals(rangOp)) {
                    if (manufactureBeginTime == 0 || manufactureEndTime == 0) {
                        throw new ValidateException(I18N.text(I18NKeys.ACTIVITY_BIG_DATE_MANUFACTURE_RANGE_IS_EMPTY));
                    } else if (manufactureBeginTime > manufactureEndTime) {
                        throw new ValidateException(I18N.text(I18NKeys.ACTIVITY_BIG_DATE_MANUFACTURE_RANGE_START_TIME_BIGGER_THAN_END_TIME));
                    } else if (toBeExpiredDays.compareTo(BigDecimal.ZERO) > 0) {
                        throw new ValidateException(I18N.text(I18NEnums.FRESHNESS_MATCHING_2.getCode()));
                    }
                }

            });
        }
    }
}
