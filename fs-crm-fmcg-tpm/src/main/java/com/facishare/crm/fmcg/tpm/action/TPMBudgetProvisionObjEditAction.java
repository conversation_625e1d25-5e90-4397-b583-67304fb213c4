package com.facishare.crm.fmcg.tpm.action;

import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.predef.action.StandardEditAction;
import lombok.extern.slf4j.Slf4j;


@SuppressWarnings("Duplicates")
@Slf4j
public class TPMBudgetProvisionObjEditAction extends StandardEditAction {


    @Override
    protected void before(Arg arg) {
        if (!actionContext.getRequestContext().isFromFunction()) {
            throw new ValidateException("edit action not allowed!");
        }
        super.before(arg);
    }
}
