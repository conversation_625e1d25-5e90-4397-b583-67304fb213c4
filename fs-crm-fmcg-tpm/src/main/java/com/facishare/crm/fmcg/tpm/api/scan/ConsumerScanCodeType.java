package com.facishare.crm.fmcg.tpm.api.scan;

import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.crm.fmcg.tpm.reward.dto.WeChatArg;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.io.Serializable;

public interface ConsumerScanCodeType {

    @Data
    @ToString
    @EqualsAndHashCode(callSuper = true)
    class Arg extends WeChatArg implements Serializable {

        private String code;
    }

    @Data
    @ToString
    @Builder
    class Result implements Serializable {

        @JSONField(name = "code_type")
        @JsonProperty(value = "code_type")
        @SerializedName("code_type")
        private String codeType;

        private String code;
    }
}