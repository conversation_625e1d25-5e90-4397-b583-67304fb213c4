package com.facishare.crm.fmcg.tpm.business;

import com.facishare.crm.fmcg.tpm.business.abstraction.IDescribeCacheService;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.dto.DescribeResult;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.locks.Lock;
import java.util.concurrent.locks.ReentrantLock;

/**
 * <AUTHOR>
 * @date 2022/4/8 下午3:37
 */
@Service
public class DescribeCacheService implements IDescribeCacheService {

    @Resource
    private ServiceFacade serviceFacade;

    private static final Cache<String, Map<String, IFieldDescribe>> FILED_CACHE;

    private static final String FILED_KEY_FORMAT = "%s.%s";

    private static final Lock LOCK = new ReentrantLock();

    static {
        FILED_CACHE = CacheBuilder.newBuilder()
                .maximumSize(1000)
                .expireAfterWrite(5, TimeUnit.MINUTES)
                .build();
    }

    @Override
    public boolean isExistField(String tenantId, String objApiName, String fieldApiName) {

        Map<String, IFieldDescribe> tenantDescribeMap = FILED_CACHE.getIfPresent(tenantId);
        String key = String.format(FILED_KEY_FORMAT, objApiName, fieldApiName);
        if (tenantDescribeMap == null) {
            LOCK.lock();
            try {
                tenantDescribeMap = FILED_CACHE.getIfPresent(tenantId);
                if (tenantDescribeMap != null) {
                    return tenantDescribeMap.get(key) != null;
                }
                tenantDescribeMap = new HashMap<>();
                DescribeResult describeResult = serviceFacade.findDescribeAndLayout(User.systemUser(tenantId), objApiName, false, null);
                tenantDescribeMap.put(key, describeResult.getObjectDescribe().getFieldDescribeMap().get(fieldApiName));
                FILED_CACHE.put(tenantId, tenantDescribeMap);
            } finally {
                LOCK.unlock();
            }
        } else {
            if (!tenantDescribeMap.containsKey(key)) {
                LOCK.lock();
                try {
                    if (tenantDescribeMap.containsKey(key)) {
                        return tenantDescribeMap.get(key) != null;
                    }
                    DescribeResult describeResult = serviceFacade.findDescribeAndLayout(User.systemUser(tenantId), objApiName, false, null);
                    tenantDescribeMap.put(key, describeResult.getObjectDescribe().getFieldDescribeMap().get(fieldApiName));
                } finally {
                    LOCK.unlock();
                }
            }
        }

        return tenantDescribeMap.get(key) != null;
    }
}
