package com.facishare.crm.fmcg.tpm.business;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.facishare.crm.fmcg.common.adapter.EnterpriseConnectionService;
import com.facishare.crm.fmcg.common.adapter.exception.RewardFmcgException;
import com.facishare.crm.fmcg.common.apiname.*;
import com.facishare.crm.fmcg.common.constant.ScanCodeActionConstants;
import com.facishare.crm.fmcg.common.utils.QueryDataUtil;
import com.facishare.crm.fmcg.common.utils.SearchQueryUtil;
import com.facishare.crm.fmcg.tpm.business.abstraction.IFmcgSerialNumberService;
import com.facishare.crm.fmcg.tpm.utils.CommonUtils;
import com.facishare.crm.fmcg.tpm.utils.I18NKeys;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.exception.MetaDataBusinessException;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.OrderBy;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.fmcg.framework.http.FMCGSnProxy;
import com.fmcg.framework.http.contract.sales.QuerySnAction;
import com.github.autoconf.ConfigFactory;
import com.google.common.base.Strings;
import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;

/**
 * Author: linmj
 * Date: 2023/9/27 16:19
 */
@Slf4j
@Component
public class FmcgSerialNumberService implements IFmcgSerialNumberService {

    @Resource
    private ServiceFacade serviceFacade;

    @Resource
    private FMCGSnProxy fmcgSnProxy;

    @Resource
    protected EnterpriseConnectionService enterpriseConnectionService;

    private static final Cache<String, Map<String, String>> actionIdCache = CacheBuilder.newBuilder().maximumSize(3000).expireAfterWrite(30, TimeUnit.MINUTES).build();

    private static final Cache<String, Map<String, String>> actionUniqueIdCache = CacheBuilder.newBuilder().maximumSize(3000).expireAfterWrite(30, TimeUnit.MINUTES).build();

    private static final Cache<String, String> statusExceptionCache = CacheBuilder.newBuilder().maximumSize(2000).expireAfterWrite(30, TimeUnit.SECONDS).build();

    private static Map<String, List<String>> ALLOW_SALES_OUT_STORE_ID_MAP = new HashMap<>();

    static {
        ConfigFactory.getConfig("fs-fmcg-tpm-config", iconfig -> {
            String jsonStr = iconfig.get("ALLOW_SALES_OUT_STORE_ID_MAP");
            if (!Strings.isNullOrEmpty(jsonStr)) {
                ALLOW_SALES_OUT_STORE_ID_MAP = JSON.parseObject(jsonStr, new TypeReference<Map<String, List<String>>>() {
                });
            }
            ALLOW_SALES_OUT_STORE_ID_MAP.putIfAbsent("777421", Lists.newArrayList("64a8cb022f35960001793186"));
            ALLOW_SALES_OUT_STORE_ID_MAP.putIfAbsent("40163027", Lists.newArrayList("65522453f856b20001c17d23"));
        });
    }

    @Override
    public List<IObjectData> queryAllSerialNumberStatusBySerialNumberId(String tenantId, String serialNumberId) {
        SearchTemplateQuery query = new SearchTemplateQuery();
        query.setOffset(0);
        query.setLimit(-1);

        Filter serialNumberFilter = new Filter();
        serialNumberFilter.setFieldName(FMCGSerialNumberStatusFields.FMCG_SERIAL_NUMBER_ID);
        serialNumberFilter.setFieldValues(Lists.newArrayList(serialNumberId));
        serialNumberFilter.setOperator(Operator.EQ);

        query.setFilters(Lists.newArrayList(serialNumberFilter));
        List<IObjectData> dataList = QueryDataUtil.find(serviceFacade, tenantId, ApiNames.FMCG_SERIAL_NUMBER_STATUS_OBJ, query, Lists.newArrayList(CommonFields.ID, FMCGSerialNumberStatusFields.FMCG_SERIAL_NUMBER_ID, FMCGSerialNumberStatusFields.ACCOUNT_ID, FMCGSerialNumberStatusFields.ACTION_ID, FMCGSerialNumberStatusFields.CURRENT_TENANT_ID, FMCGSerialNumberStatusFields.PERSONNEL_ID, FMCGSerialNumberStatusFields.NEXT_BUSINESS_ID, FMCGSerialNumberStatusFields.CHANNEL_TYPE, FMCGSerialNumberStatusFields.PERSONNEL_API_NAME, FMCGSerialNumberStatusFields.BUSINESS_OCCURRENCE_TIME, FMCGSerialNumberStatusFields.BUSINESS_OBJECT_NAME, FMCGSerialNumberStatusFields.BUSINESS_OBJECT_ID, FMCGSerialNumberStatusFields.BUSINESS_OBJECT, CommonFields.OBJECT_DESCRIBE_API_NAME));
        dataList.sort((a, b) -> -a.get(FMCGSerialNumberStatusFields.BUSINESS_OCCURRENCE_TIME, Long.class, 0L).compareTo(b.get(FMCGSerialNumberStatusFields.BUSINESS_OCCURRENCE_TIME, Long.class, 0L)));

        return dataList;
    }

    @Override
    public IObjectData querySerialNumberByName(String tenantId, String name) {
        if (Strings.isNullOrEmpty(name)) {
            return null;
        }
        IFilter idFilter = new Filter();
        idFilter.setFieldName(CommonFields.NAME);
        idFilter.setOperator(Operator.EQ);
        idFilter.setFieldValues(Lists.newArrayList(name));

        SearchTemplateQuery query = QueryDataUtil.minimumQuery(idFilter);
        query.setLimit(1);

        List<IObjectData> data = QueryDataUtil.find(serviceFacade, tenantId, ApiNames.FMCG_SERIAL_NUMBER_OBJ, query,
                Lists.newArrayList("_id", CommonFields.TENANT_ID, FMCGSerialNumberFields.PRODUCT_ID, FMCGSerialNumberFields.MANUFACTURE_DATE)
        );
        if (CollectionUtils.isEmpty(data)) {
            return null;
        }
        return data.get(0);
    }

    @Override
    public IObjectData getStoreSignSerialNumberStatusObj(String tenantId, String snCodeId, boolean enableSablesOutLogic) {
        String storeSignActionId = getActionIdByActionUniqueId(tenantId, ScanCodeActionConstants.STORE_SIGN);
        String returnGoodsActionId = getActionIdByActionUniqueId(tenantId, ScanCodeActionConstants.RETURN_GOODS);
        String exchangeReturnActionId = getActionIdByActionUniqueId(tenantId, ScanCodeActionConstants.EXCHANGE_RETURN_IN);
        String storeCheckActionId = getActionIdByActionUniqueId(tenantId, ScanCodeActionConstants.STORE_STOCK_CHECK);
        List<String> storeSignActionIds = Lists.newArrayList(storeSignActionId, storeCheckActionId);
        List<IObjectData> actions = getActionsDescByActionIds(tenantId, snCodeId, Lists.newArrayList(storeSignActionId, returnGoodsActionId, exchangeReturnActionId, storeCheckActionId));
        try {
            if (CollectionUtils.isEmpty(actions)) {
                throw new RewardFmcgException("10010", I18N.text(I18NKeys.REWARD_FMCG_SERIAL_NUMBER_SERVICE_2));
            }
            String lastStatusActionId = actions.get(0).get(FMCGSerialNumberStatusFields.ACTION_ID, String.class);
            if (returnGoodsActionId.equals(lastStatusActionId) || exchangeReturnActionId.equals(lastStatusActionId)) {
                throw new RewardFmcgException("10010", I18N.text(I18NKeys.REWARD_FMCG_SERIAL_NUMBER_SERVICE_3));
            }

            return actions.stream().filter(status -> storeSignActionIds.contains(status.get(FMCGSerialNumberStatusFields.ACTION_ID, String.class)))
                    .findFirst().orElseThrow(() -> new RewardFmcgException("10010", "商品码未签收。"));//ignorei18n
        } catch (RewardFmcgException e) {
            if (enableSablesOutLogic) {
                return getSalesOutSerialNumberStatusObj(tenantId, snCodeId);
            }
            throw e;
        }

    }

    private IObjectData getSalesOutSerialNumberStatusObj(String tenantId, String snCodeId) {
        List<String> allowList = getSalesOutStoreIds(tenantId);
        if (CollectionUtils.isEmpty(allowList)) {
            log.info("tenantId:{},allowList is empty.", tenantId);
            throw new RewardFmcgException("10010", I18N.text(I18NKeys.REWARD_FMCG_SERIAL_NUMBER_SERVICE_2));
        }
        String salesOutActionId = getActionIdByActionUniqueId(tenantId, ScanCodeActionConstants.SALES_OUT_OF_WAREHOUSE);
        List<IObjectData> actions = getActionsDescByActionIds(tenantId, snCodeId, Lists.newArrayList(salesOutActionId));
        if (CollectionUtils.isEmpty(actions)) {
            log.info("tenantId:{},sales Out actions is empty.", tenantId);
            throw new RewardFmcgException("10010", I18N.text(I18NKeys.REWARD_FMCG_SERIAL_NUMBER_SERVICE_2));
        }
        IObjectData status = actions.get(0);
        String currentTenantId = status.get(FMCGSerialNumberStatusFields.CURRENT_TENANT_ID, String.class);
        String storeId = enterpriseConnectionService.getStoreId(tenantId, currentTenantId);
        if (!allowList.contains(storeId)) {
            log.info("tenantId:{},storeId:{},allowList:{}", tenantId, storeId, allowList);
            throw new RewardFmcgException("10010", I18N.text(I18NKeys.REWARD_FMCG_SERIAL_NUMBER_SERVICE_2));
        }
        //替换门店为经销商门店
        status.set(FMCGSerialNumberStatusFields.ACCOUNT_ID, storeId);
        log.info("use sale out logic,tenantId:{},snCodeId:{},storeId:{}", tenantId, snCodeId, storeId);
        return status;
    }

    private List<String> getSalesOutStoreIds(String tenantId) {
        return ALLOW_SALES_OUT_STORE_ID_MAP.getOrDefault(tenantId, Lists.newArrayList());
    }

    private List<IObjectData> getActionsDescByActionIds(String tenantId, String snCodeId, List<String> actionIds) {
        SearchTemplateQuery query = new SearchTemplateQuery();
        query.setLimit(100);
        query.setOffset(0);

        Filter codeFilter = new Filter();
        codeFilter.setFieldName(FMCGSerialNumberStatusFields.FMCG_SERIAL_NUMBER_ID);
        codeFilter.setOperator(Operator.EQ);
        codeFilter.setFieldValues(Lists.newArrayList(snCodeId));

        Filter actionFilter = new Filter();
        actionFilter.setFieldName(FMCGSerialNumberStatusFields.ACTION_ID);
        actionFilter.setOperator(Operator.IN);
        actionFilter.setFieldValues(actionIds);

        query.setFilters(Lists.newArrayList(codeFilter, actionFilter));

        List<IObjectData> actions = QueryDataUtil.find(serviceFacade, tenantId, ApiNames.FMCG_SERIAL_NUMBER_STATUS_OBJ, query, Lists.newArrayList(CommonFields.ID, FMCGSerialNumberStatusFields.CHANNEL_TYPE, FMCGSerialNumberStatusFields.FMCG_SERIAL_NUMBER_ID, FMCGSerialNumberStatusFields.ACCOUNT_ID, FMCGSerialNumberStatusFields.ACTION_ID, FMCGSerialNumberStatusFields.CURRENT_TENANT_ID, FMCGSerialNumberStatusFields.PERSONNEL_API_NAME, FMCGSerialNumberStatusFields.PERSONNEL_ID, FMCGSerialNumberStatusFields.NEXT_BUSINESS_ID, FMCGSerialNumberStatusFields.BUSINESS_OBJECT_ID, FMCGSerialNumberStatusFields.BUSINESS_OCCURRENCE_TIME, CommonFields.OBJECT_DESCRIBE_API_NAME));

        actions.sort((a, b) -> -a.get(FMCGSerialNumberStatusFields.BUSINESS_OCCURRENCE_TIME, Long.class, 0L).compareTo(b.get(FMCGSerialNumberStatusFields.BUSINESS_OCCURRENCE_TIME, Long.class, 0L)));
        return actions;
    }


    @Override
    public IObjectData getProductObjFromSerialNumberObj(String tenantId, String productId) {
        if (Strings.isNullOrEmpty(productId)) {
            throw new MetaDataBusinessException(I18N.text(I18NKeys.METADATA_FMCG_SERIAL_NUMBER_SERVICE_0));
        }

        IFilter idFilter = new Filter();
        idFilter.setFieldName("_id");
        idFilter.setOperator(Operator.EQ);
        idFilter.setFieldValues(Lists.newArrayList(productId));

        SearchTemplateQuery query = QueryDataUtil.minimumQuery(idFilter);
        query.setLimit(1);

        List<IObjectData> data = QueryDataUtil.find(serviceFacade, tenantId, "ProductObj", query,
                Lists.newArrayList("_id", ProductFields.NAME, ProductFields.PRODUCT_CODE, ProductFields.QUALITY_GUARANTEE_PERIOD)
        );
        if (CollectionUtils.isEmpty(data)) {
            throw new MetaDataBusinessException(I18N.text(I18NKeys.METADATA_FMCG_SERIAL_NUMBER_SERVICE_1));
        }
        return data.get(0);
    }

    @Override
    public IObjectData getSerialNumberObjByRealCode(String tenantId, String realCode) {
        IFilter codeFilter = new Filter();
        codeFilter.setFieldName(FMCGSerialNumberFields.CODE_TRUE);
        codeFilter.setOperator(Operator.EQ);
        codeFilter.setFieldValues(Lists.newArrayList(realCode));

        SearchTemplateQuery query = QueryDataUtil.minimumQuery(codeFilter);
        query.setLimit(1);

        List<IObjectData> data = QueryDataUtil.find(serviceFacade, tenantId, ApiNames.FMCG_SERIAL_NUMBER_OBJ, query,
                Lists.newArrayList("_id", FMCGSerialNumberFields.PRODUCT_ID, FMCGSerialNumberFields.MANUFACTURE_DATE)
        );
        if (CollectionUtils.isEmpty(data)) {
            throw new RewardFmcgException("10010", I18N.text(I18NKeys.REWARD_FMCG_SERIAL_NUMBER_SERVICE_4));
        }
        return data.get(0);
    }

    @Override
    public String getActionIdByActionUniqueId(String tenantId, String actionUniqueId) {
        Map<String, String> map = actionIdCache.getIfPresent(tenantId);
        if (map == null) {
            map = new HashMap<>();
            List<QuerySnAction.FMCGSerialNumberActionEntity> actionEntities = getActions(tenantId, actionUniqueId);
            if (!CollectionUtils.isEmpty(actionEntities)) {
                for (QuerySnAction.FMCGSerialNumberActionEntity action : actionEntities) {
                    map.put(action.getUniqueId(), action.getId());
                }
            }
            actionIdCache.put(tenantId, map);
        } else {
            if (map.get(actionUniqueId) == null) {
                List<QuerySnAction.FMCGSerialNumberActionEntity> actionEntities = getActions(tenantId, actionUniqueId);
                if (!CollectionUtils.isEmpty(actionEntities)) {
                    for (QuerySnAction.FMCGSerialNumberActionEntity action : actionEntities) {
                        map.put(action.getUniqueId(), action.getId());
                    }
                }
            }
        }
        String id = map.get(actionUniqueId);
        log.info("actionUniqueId:{},id:{}", actionUniqueId, id);
        return id;
    }

    @Override
    public String getActionUniqueIdByActionId(String tenantId, String actionId) {
        if (Strings.isNullOrEmpty(actionId)) {
            return null;
        }
        Map<String, String> map = actionUniqueIdCache.getIfPresent(tenantId);
        if (map == null) {
            map = new HashMap<>();
            List<QuerySnAction.FMCGSerialNumberActionEntity> actionEntities = getActions(tenantId, null);
            if (!CollectionUtils.isEmpty(actionEntities)) {
                for (QuerySnAction.FMCGSerialNumberActionEntity action : actionEntities) {
                    map.put(action.getId(), action.getUniqueId());
                }
            }
            actionUniqueIdCache.put(tenantId, map);
        } else {
            if (map.get(actionId) == null) {
                List<QuerySnAction.FMCGSerialNumberActionEntity> actionEntities = getActions(tenantId, null);
                if (!CollectionUtils.isEmpty(actionEntities)) {
                    for (QuerySnAction.FMCGSerialNumberActionEntity action : actionEntities) {
                        map.put(action.getId(), action.getUniqueId());
                    }
                }
            }
        }
        String id = map.get(actionId);
        log.info("actionUniqueId:{},id:{}", actionId, id);
        return id;
    }

    @Override
    public String getLastStatusExceptionTypes(String tenantId, String snId, String actionId) {
        String key = String.format("%s_%s_%s", tenantId, snId, actionId);
        try {
            return statusExceptionCache.get(key, () -> {
                SearchTemplateQuery query = SearchQueryUtil.formSimpleQuery(0, 1, Lists.newArrayList(
                        SearchQueryUtil.filter(FMCGSerialNumberStatusFields.ACTION_ID, Operator.EQ, Lists.newArrayList(actionId)),
                        SearchQueryUtil.filter(FMCGSerialNumberStatusFields.FMCG_SERIAL_NUMBER_ID, Operator.EQ, Lists.newArrayList(snId)),
                        SearchQueryUtil.filter(FMCGSerialNumberStatusFields.WHETHER_ABNORMAL, Operator.EQ, Lists.newArrayList("true"))
                ));
                query.setOrders(Lists.newArrayList(new OrderBy(FMCGSerialNumberStatusFields.BUSINESS_OCCURRENCE_TIME, false)));
                List<IObjectData> data = CommonUtils.queryData(serviceFacade, User.systemUser(tenantId), ApiNames.FMCG_SERIAL_NUMBER_STATUS_OBJ, query,
                        Lists.newArrayList(CommonFields.ID, FMCGSerialNumberStatusFields.WHETHER_ABNORMAL, FMCGSerialNumberStatusFields.EXCEPTION_TYPE, FMCGSerialNumberStatusFields.BUSINESS_OCCURRENCE_TIME, FMCGSerialNumberStatusFields.ACTION_ID, CommonFields.OBJECT_DESCRIBE_API_NAME));
                if (CollectionUtils.isNotEmpty(data)) {
                    log.info("data:{}", data);
                    return data.get(0).get(FMCGSerialNumberStatusFields.EXCEPTION_TYPE, String.class);
                }
                return "";
            });
        } catch (ExecutionException e) {
            log.info("get last status exception types error", e);
            throw new ValidateException("获取码状态异常失败。");//ignorei18n
        }
    }

    private List<QuerySnAction.FMCGSerialNumberActionEntity> getActions(String tenantId, String actionUniqueId) {
        QuerySnAction.Arg querySnActionArg = new QuerySnAction.Arg();
        if (!Strings.isNullOrEmpty(actionUniqueId)) {
            querySnActionArg.setUniqueId(actionUniqueId);
        }
        QuerySnAction.Result querySnActionResult = fmcgSnProxy.querySnAction(Integer.valueOf(tenantId), -10000, querySnActionArg);
        if (querySnActionResult.getErrCode() != 0) {
            throw new RewardFmcgException(String.valueOf(querySnActionResult.getErrCode()), querySnActionResult.getErrMessage());
        }
        return querySnActionResult.getResult().getActions();
    }
}
