package com.facishare.crm.fmcg.tpm.business;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.facishare.common.parallel.ParallelUtils;
import com.facishare.crm.fmcg.common.constant.ScanCodeActionConstants;
import com.facishare.crm.fmcg.common.utils.QueryDataUtil;
import com.facishare.crm.fmcg.tpm.api.rule.WxMerchantAccountDTO;
import com.facishare.crm.fmcg.tpm.reward.abstraction.ActivityRewardBase;
import com.facishare.crm.fmcg.tpm.utils.I18NKeys;
import com.facishare.organization.api.model.department.arg.GetDepartmentDtoArg;
import com.facishare.organization.api.service.DepartmentProviderService;
import com.facishare.paas.I18N;
import com.facishare.converter.EIEAConverter;
import com.facishare.crm.fmcg.common.adapter.EnterpriseConnectionService;
import com.facishare.crm.fmcg.common.adapter.abstraction.IPayService;
import com.facishare.crm.fmcg.common.adapter.dto.UserInfo;
import com.facishare.crm.fmcg.common.adapter.dto.pay.TransferDetail;
import com.facishare.crm.fmcg.common.adapter.dto.pay.TransferDetailStatusEnum;
import com.facishare.crm.fmcg.common.adapter.dto.pay.cloud.CloudAccount;
import com.facishare.crm.fmcg.common.adapter.dto.pay.cloud.CloudTransfer;
import com.facishare.crm.fmcg.common.adapter.dto.pay.cloud.QueryCloudTransferDetails;
import com.facishare.crm.fmcg.common.adapter.dto.pay.cloud.WXCloudPayReceiverAccount;
import com.facishare.crm.fmcg.common.adapter.dto.pay.wx.BatchWXTenantTransfer;
import com.facishare.crm.fmcg.common.adapter.dto.pay.wx.QueryWXTenantTransferDetail;
import com.facishare.crm.fmcg.common.adapter.dto.pay.wx.WXPersonalAccount;
import com.facishare.crm.fmcg.common.adapter.dto.pay.wx.WXTenantAccount;
import com.facishare.crm.fmcg.common.adapter.exception.RewardFmcgException;
import com.facishare.crm.fmcg.common.apiname.*;
import com.facishare.crm.fmcg.common.utils.IdentityIdGenerator;
import com.facishare.crm.fmcg.common.utils.SearchQueryUtil;
import com.facishare.crm.fmcg.tpm.business.abstraction.IRedPacketService;
import com.facishare.crm.fmcg.tpm.business.dto.GetRewardDetailDTO;
import com.facishare.crm.fmcg.tpm.business.dto.ReceiverInfoDTO;
import com.facishare.crm.fmcg.tpm.dao.mongo.ActivityRewardRuleDAO;
import com.facishare.crm.fmcg.tpm.dao.mongo.BizCodeDAO;
import com.facishare.crm.fmcg.tpm.dao.mongo.po.ActivityRewardRulePO;
import com.facishare.crm.fmcg.tpm.dao.mongo.po.BizCodePO;
import com.facishare.crm.fmcg.tpm.dao.mongo.po.RewardDetailEntity;
import com.facishare.crm.fmcg.tpm.retry.setter.RedPacketStatusSetter;
import com.facishare.crm.fmcg.tpm.retry.setter.RedPacketWithdrawSetter;
import com.facishare.crm.fmcg.tpm.retry.setter.RewardRecordSetter;
import com.facishare.crm.fmcg.tpm.retry.setter.RewardRuleRedPacketSetter;
import com.facishare.crm.fmcg.tpm.reward.handler.BigDateHandler;
import com.facishare.crm.fmcg.tpm.service.TransactionProxy;
import com.facishare.crm.fmcg.tpm.utils.CommonUtils;
import com.facishare.crm.fmcg.tpm.web.service.TenantHierarchyService;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.log.ActionType;
import com.facishare.paas.appframework.log.EventType;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.metadata.api.DBRecord;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.github.autoconf.ConfigFactory;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * Author: linmj
 * Date: 2023/10/16 18:36
 */
//IgnoreI18nFile
@Slf4j
@Service
public class RedPacketService implements IRedPacketService {

    @Resource
    private IPayService payService;

    @Resource
    private ServiceFacade serviceFacade;

    @Resource
    private RedissonClient redissonCmd;

    @Resource
    private EIEAConverter eieaConverter;

    @Resource
    private RewardRuleRedPacketSetter rewardRuleRedPacketSetter;

    @Resource
    private RedPacketWithdrawSetter redPacketWithdrawSetter;

    @Resource
    private RedPacketStatusSetter redPacketStatusSetter;

    @Resource
    private BigDateHandler bigDateHandler;

    @Resource
    private BizCodeDAO bizCodeDAO;

    @Resource
    private ActivityRewardRuleDAO activityRewardRuleDAO;

    @Resource
    private TransactionProxy transactionProxy;

    @Resource
    private EnterpriseConnectionService enterpriseConnectionService;

    @Resource
    private RewardRecordSetter rewardRecordSetter;

    @Resource
    private TenantHierarchyService tenantHierarchyService;

    @Resource
    protected DepartmentProviderService departmentProviderService;

    public static final List<String> NEED_REASON_STATUS = Lists.newArrayList("4", "WAIT_PAY");

    private static Map<String, String> TENANT_CODE_TO_TENANT_MAP = new HashMap<>();

    private static Map<String, String> TENANT_TO_TENANT_CODE_MAP = new HashMap<>();

    static {
        ConfigFactory.getConfig("fs-fmcg-tpm-config").addListener(config -> {
            String json = config.get("tenant_code_to_tenant");
            if (!Strings.isNullOrEmpty(json)) {
                JSONObject map = JSON.parseObject(json);
                Map<String, String> newMap = new HashMap<>();
                map.forEach((k, v) -> newMap.put(k, v.toString()));
                TENANT_CODE_TO_TENANT_MAP = newMap;
                TENANT_TO_TENANT_CODE_MAP = newMap.entrySet().stream().collect(Collectors.toMap(Map.Entry::getValue, Map.Entry::getKey));
            }
        });
    }


    public boolean refreshRewardStatus(IObjectData redPacket) {
        String status = redPacket.get(RedPacketRecordObjFields.PAYMENT_STATUS, String.class);
        switch (RedPacketPaymentStatusEnum.codeOf(status)) {
            case INIT:
            case PUBLISH_FAIL:
            case FAIL:
                return republish(redPacket);
            case PROCESSING:
                return queryRecordStatus(redPacket);
            default:

        }
        return false;
    }

    @Override
    public void refreshRedPacketInfo(String tenantId, String redPacketId) {
        User sysUser = User.systemUser(tenantId);
        tryLock(redPacketId);
        try {
            IObjectData redPacket = serviceFacade.findObjectData(sysUser, redPacketId, ApiNames.RED_PACKET_RECORD_OBJ);
            String status = redPacket.get(RedPacketRecordObjFields.PAYMENT_STATUS, String.class);
            Long rewardTime = redPacket.get(RedPacketRecordObjFields.REWARD_TIME, Long.class);
            Long expiredTime = redPacket.get(RedPacketRecordObjFields.EXPIRATION_TIME, Long.class);
            String distributeWay = redPacket.get(RedPacketRecordObjFields.DISTRIBUTE_WAY, String.class);
            if (!RedPacketRecordObjFields.PaymentStatus.ERROR.equals(status) && RedPacketRecordObjFields.DistributeWay.AUTO.equals(distributeWay)) {
                throw new ValidateException(I18N.text(I18NKeys.RED_PACKET_SERVICE_0));
            } else if (RedPacketRecordObjFields.DistributeWay.ARTIFICIAL.equals(distributeWay) && !RedPacketRecordObjFields.WithdrawalStatus.AWAIT.equals(redPacket.get(RedPacketRecordObjFields.WITHDRAWAL_STATUS, String.class))) {
                throw new ValidateException(I18N.text(I18NKeys.RED_PACKET_SERVICE_1));
            }

            String activityId = redPacket.get(RedPacketRecordObjFields.ACTIVITY_ID, String.class);
            String rewardDetailId = redPacket.get(RedPacketRecordObjFields.REWARD_DETAIL_ID, String.class);
            String snStatusId = redPacket.get(RedPacketRecordObjFields.SERIAL_NUMBER_STATUS_ID, String.class);
            String businessId = redPacket.get(RedPacketRecordObjFields.BUSINESS_ID, String.class);
            if (Strings.isNullOrEmpty(activityId) || Strings.isNullOrEmpty(rewardDetailId) || Strings.isNullOrEmpty(snStatusId)) {
                log.info("activityId:{},rewardDetailId:{},statusId:{}", activityId, rewardDetailId, snStatusId);
                return;
            }
            ActivityRewardRulePO rewardRulePO = activityRewardRuleDAO.getByRelatedObject(tenantId, ApiNames.TPM_ACTIVITY_OBJ, activityId);
            RewardDetailEntity rewardDetail = rewardRulePO.getRewardDetails().stream().filter(detail -> rewardDetailId.equals(detail.getDetailId())).findFirst().orElse(null);
            IObjectData snStatusObj = serviceFacade.findObjectData(sysUser, snStatusId, ApiNames.FMCG_SERIAL_NUMBER_STATUS_OBJ);
            GetRewardDetailDTO rewardDetailDTO = bigDateHandler.getRewardDetail(tenantId, activityId, businessId, rewardDetail, snStatusObj);
            rewardDetailDTO.getRedPacket().setId(redPacketId);
            rewardDetailDTO.getRedPacketDetails().forEach(detail -> detail.set(RedPacketRecordDetailObjFields.RED_PACKET_RECORD_ID, redPacketId));
            rewardDetailDTO.getRedPacket().set(RedPacketRecordObjFields.PAYMENT_STATUS, RedPacketRecordObjFields.PaymentStatus.INIT);
            rewardDetailDTO.getRedPacket().set(RedPacketRecordObjFields.RETRY_TIMES, 0);
            rewardDetailDTO.getRedPacket().set(RedPacketRecordObjFields.REWARD_TIME, rewardTime);
            rewardDetailDTO.getRedPacket().set(RedPacketRecordObjFields.PAYMENT_ERROR_MESSAGE, "");
            rewardDetailDTO.getRedPacket().set(RedPacketRecordObjFields.EXPIRATION_TIME, expiredTime);
            rewardDetailDTO.getActivityRewardDetails().forEach(detail -> detail.set(TPMActivityRewardDetailFields.REWARD_TIME, rewardTime));
            //ObjectDataExt.of(redPacket).putAll(ObjectDataExt.toMap(rewardDetailDTO.getRedPacket()));

            transactionProxy.run(() -> {
                serviceFacade.updateWithMap(sysUser, redPacket, ObjectDataExt.toMap(rewardDetailDTO.getRedPacket()));
                List<IObjectData> olds = serviceFacade.findDetailObjectDataList(serviceFacade.findObject(tenantId, ApiNames.RED_PACKET_RECORD_DETAIL_OBJ), redPacket, sysUser);

                serviceFacade.bulkSaveObjectData(rewardDetailDTO.getRedPacketDetails(), sysUser);
                serviceFacade.bulkSaveObjectData(rewardDetailDTO.getActivityRewardDetails(), sysUser);

                invalidRewardDetailByRedPacketDetail(tenantId, olds);
                serviceFacade.bulkInvalid(olds, sysUser);


                if (RedPacketRecordObjFields.DistributeWay.AUTO.equals(distributeWay)) {
                    rewardRuleRedPacketSetter.setUpdateStatusTask(redPacket.getTenantId(), redPacket.getId());
                } else if (RedPacketRecordObjFields.DistributeWay.ARTIFICIAL.equals(distributeWay)) {
                    redPacketStatusSetter.setUpdateStatusTask(redPacket.getTenantId(), redPacket.getId(), redPacket.get(RedPacketRecordObjFields.EXPIRATION_TIME, Long.class, 0L));
                }
            });
        } finally {
            unlock(redPacketId);
        }
    }

    @Override
    public void refreshAccountInfo(String tenantId, String redPacketId) {
        User sysUser = User.systemUser(tenantId);
        IObjectData redPacket = serviceFacade.findObjectData(sysUser, redPacketId, ApiNames.RED_PACKET_RECORD_OBJ);
        String status = redPacket.get(RedPacketRecordObjFields.PAYMENT_STATUS, String.class);
        String msg = redPacket.get(RedPacketRecordObjFields.PAYMENT_ERROR_MESSAGE, String.class);
        String distributeWay = redPacket.get(RedPacketRecordObjFields.DISTRIBUTE_WAY, String.class);
        if (RedPacketRecordObjFields.PaymentStatus.TRANSFERRING.equals(status) && "业务服务费余额不足".equals(msg)) {
            rewardRuleRedPacketSetter.setUpdateStatusTask(redPacket.getTenantId(), redPacket.getId(), System.currentTimeMillis(), true, null);
            return;
        }
        if (!RedPacketRecordObjFields.PaymentStatus.ERROR.equals(status) && RedPacketRecordObjFields.DistributeWay.AUTO.equals(distributeWay)) {
            throw new ValidateException(I18N.text(I18NKeys.RED_PACKET_SERVICE_2));
        } else if (RedPacketRecordObjFields.DistributeWay.ARTIFICIAL.equals(distributeWay) && !RedPacketRecordObjFields.WithdrawalStatus.AWAIT.equals(redPacket.get(RedPacketRecordObjFields.WITHDRAWAL_STATUS, String.class))) {
            throw new ValidateException(I18N.text(I18NKeys.RED_PACKET_SERVICE_3));
        }

        String role = redPacket.get(RedPacketRecordObjFields.ROLE, String.class);
        // 角色不为空 自定义字段角色
        if (!Strings.isNullOrEmpty(role)) {
            try {
                fillUserInfo(redPacket, role);
            } catch (Exception ex) {
                log.info("fillUserInfo error ..", ex);
            }
        }

        String userInfo = redPacket.get(RedPacketRecordObjFields.REWARDED_PERSON_ID, String.class);
        String rewardPerson = redPacket.get(RedPacketRecordObjFields.REWARDED_PERSON, String.class);
        // 奖励人ID为空 且 角色 不是 消费者 (1)。兼容消费者重发的情况。
        if (Strings.isNullOrEmpty(userInfo) && !"1".equals(role)) {
            throw new RuntimeException("奖励人ID为空 不适用此接口更新账户信息。");
        }

        try {
            if (userInfo != null && userInfo.contains(".") && !rewardPerson.equals("消费者")) {
                String[] infos = userInfo.split("\\.");
                String receiveTenantId = redPacket.get(RedPacketRecordObjFields.TRANSFEREE_TENANT_ID, String.class);
                String userTenantId = infos[0];
                String userId = infos[1];
                tryLock(redPacketId);
                IObjectData user;
                if (receiveTenantId.equals(userTenantId)) {
                    user = serviceFacade.findObjectData(User.systemUser(receiveTenantId), userId, ApiNames.PERSONNEL_OBJ);
                } else {
                    user = enterpriseConnectionService.getContactObjByPublicEmployeeId(receiveTenantId, userId);
                }
                ReceiverInfoDTO receiverInfo = new ReceiverInfoDTO();
                bigDateHandler.fillReceiverIdentityInfo(tenantId, user, receiverInfo);
                Map<String, Object> updateMap = new HashMap<>();
                updateMap.put(RedPacketRecordObjFields.TRANSFEREE_ID, receiverInfo.getIdCard());
                updateMap.put(RedPacketRecordObjFields.TRANSFEREE_PHONE, receiverInfo.getPhone());
                updateMap.put(RedPacketRecordObjFields.TRANSFEREE_NAME, receiverInfo.getName());
                updateMap.put(RedPacketRecordObjFields.TRANSFEREE_WECHAT_OPEN_ID, receiverInfo.getWxOpenId());
                updateMap.put(RedPacketRecordObjFields.TRANSFEREE_WECHAT_APP_ID, receiverInfo.getWxAppId());
                updateMap.put(RedPacketRecordObjFields.TRANSFEREE_WX_UNION_ID, receiverInfo.getUnionId());
                updateMap.put(RedPacketRecordObjFields.PAYMENT_STATUS, RedPacketRecordObjFields.PaymentStatus.INIT);
                updateMap.put(RedPacketRecordObjFields.RETRY_TIMES, 0);
                updateMap.put(RedPacketRecordObjFields.PAYMENT_ERROR_MESSAGE, "");
                serviceFacade.updateWithMap(sysUser, redPacket, updateMap);
            } else {
                Map<String, Object> updateMap = new HashMap<>();
                String relatedObjectApiName = redPacket.get(RedPacketRecordObjFields.RELATED_OBJECT_API_NAME, String.class);
                String remark = redPacket.get(RedPacketRecordObjFields.REMARKS, String.class);
                if (ApiNames.POINTS_EXCHANGE_RECORD_OBJ.equals(relatedObjectApiName) && Strings.isNullOrEmpty(remark)) {
                    updateMap.put(RedPacketRecordObjFields.REMARKS, "活动红包奖励");
                }
                updateMap.put(RedPacketRecordObjFields.PAYMENT_STATUS, RedPacketRecordObjFields.PaymentStatus.INIT);
                updateMap.put(RedPacketRecordObjFields.RETRY_TIMES, 0);
                updateMap.put(RedPacketRecordObjFields.PAYMENT_ERROR_MESSAGE, "");
                serviceFacade.updateWithMap(sysUser, redPacket, updateMap);
                log.info("消费者场景");
            }
            if (RedPacketRecordObjFields.DistributeWay.AUTO.equals(distributeWay)) {
                rewardRuleRedPacketSetter.setUpdateStatusTask(redPacket.getTenantId(), redPacket.getId());
            } else if (RedPacketRecordObjFields.DistributeWay.ARTIFICIAL.equals(distributeWay)) {
                redPacketStatusSetter.setUpdateStatusTask(redPacket.getTenantId(), redPacket.getId(), redPacket.get(RedPacketRecordObjFields.EXPIRATION_TIME, Long.class, 0L));
            }
        } finally {
            unlock(redPacketId);
        }
    }

    private void fillUserInfo(IObjectData redPacket, String eventRole) {
        String personId = redPacket.get(RedPacketRecordObjFields.REWARDED_PERSON_ID, String.class);
        if (!Strings.isNullOrEmpty(personId)) {
            return;
        }
        switch (eventRole) {
            case "1":
                log.warn("consumer reward retry : {}", redPacket.getName());
                break;
            case "2":
                reloadStoreOwnerToAccountInformation(redPacket);
                break;
            case "3":
                reloadSalesmenToAccountInformation(redPacket);
                break;
            case "5":
                reloadMBossToAccountInformation(redPacket);
                break;
            default:
                break;
        }


    }

    private void reloadMBossToAccountInformation(IObjectData redPacket) {

        String eventTenantId = redPacket.get(RedPacketRecordObjFields.RELATED_OBJECT_TENANT_ID, String.class);

        GetDepartmentDtoArg arg = new GetDepartmentDtoArg();
        arg.setDepartmentId(999999);
        arg.setEnterpriseId(Integer.parseInt(eventTenantId));
        Integer principalId = departmentProviderService.getDepartmentDto(arg).getDepartment().getPrincipalId();
        if (principalId != null) {
            String rewardPersonId = String.format("%s.%s", eventTenantId, principalId);
            redPacket.set(RedPacketRecordObjFields.REWARDED_PERSON_ID, rewardPersonId);
        }
    }

    private void reloadSalesmenToAccountInformation(IObjectData redPacket) {

    }

    private void reloadStoreOwnerToAccountInformation(IObjectData redPacket) {
        String eventTenantId = redPacket.get(RedPacketRecordObjFields.RELATED_OBJECT_TENANT_ID, String.class);
        String eventStoreId = redPacket.get(RedPacketRecordObjFields.ACCOUNT_ID, String.class);

        IFilter storeFilter = new Filter();
        storeFilter.setFieldName("account_id");
        storeFilter.setOperator(Operator.EQ);
        storeFilter.setFieldValues(Lists.newArrayList(eventStoreId));

        IFilter storeOwnerFlagFilter = new Filter();
        storeOwnerFlagFilter.setFieldName("field_2m89p__c");
        storeOwnerFlagFilter.setOperator(Operator.EQ);
        storeOwnerFlagFilter.setFieldValues(Lists.newArrayList("1"));

        SearchTemplateQuery stq = QueryDataUtil.minimumQuery(storeFilter, storeOwnerFlagFilter);
        stq.setLimit(1);

        List<IObjectData> data = QueryDataUtil.find(serviceFacade, eventTenantId, "ContactObj", stq, Lists.newArrayList(
                "_id",
                CommonFields.TENANT_ID,
                ContactFields.NAME,
                ContactFields.PUBLIC_EMPLOYEE_ID
        ));
        if (CollectionUtils.isNotEmpty(data)) {
            IObjectData objectData = data.get(0);
            String publicEmployeeId = objectData.get(ContactFields.PUBLIC_EMPLOYEE_ID, String.class);
            IObjectData publicEmployeeObj = serviceFacade.findObjectDataIgnoreAll(User.systemUser(eventTenantId), publicEmployeeId, ApiNames.PUBLIC_EMPLOYEE_OBJ);
            if (Objects.nonNull(publicEmployeeObj)) {
                String rewardPersonId = String.format("%s.%s", publicEmployeeObj.get(PublicEmployeeFields.OUTER_TENANT_ID, String.class), publicEmployeeObj.getId());
                redPacket.set(RedPacketRecordObjFields.REWARDED_PERSON_ID, rewardPersonId);
            }
        }
    }

    @Override
    public void refreshProcessingRedPacket(String tenantId) {
        long leastTime = 1709111641450L;
        LocalDateTime lastMonth = LocalDateTime.now().minusMonths(1);
        long queryTime = Math.max(leastTime, lastMonth.toInstant(ZoneOffset.of("+8")).toEpochMilli());
        ParallelUtils.createParallelTask().submit(() -> {
            SearchTemplateQuery query = SearchQueryUtil.formSimpleQuery(0, -1,
                    Lists.newArrayList(SearchQueryUtil.filter(RedPacketRecordObjFields.PAYMENT_STATUS, Operator.EQ, Lists.newArrayList(RedPacketRecordObjFields.PaymentStatus.TRANSFERRING)),
                            SearchQueryUtil.filter(RedPacketRecordObjFields.DISTRIBUTE_WAY, Operator.EQ, Lists.newArrayList(RedPacketRecordObjFields.DistributeWay.AUTO)),
                            SearchQueryUtil.filter(CommonFields.CREATE_TIME, Operator.GT, Lists.newArrayList(String.valueOf(queryTime))))
            );
            log.info("query :{}", JSON.toJSONString(query));
            CommonUtils.executeInAllDataWithFields(serviceFacade, User.systemUser(tenantId), ApiNames.RED_PACKET_RECORD_OBJ, query, Lists.newArrayList(CommonFields.ID), (dataList) -> {
                log.info("data size:{}", dataList.size());
                dataList.forEach(data -> {
                    try {
                        rewardRuleRedPacketSetter.setUpdateStatusTask(tenantId, data.getId(), 0L, true, 1);
                    } catch (Exception e) {
                        log.info("rewardRuleRedPacketSetter.setUpdateStatusTask err", e);
                    }
                });
            });
            log.info("preset redPacket done!");
        }).run();
        ParallelUtils.createParallelTask().submit(() -> {
            SearchTemplateQuery query = SearchQueryUtil.formSimpleQuery(0, -1,
                    Lists.newArrayList(SearchQueryUtil.filter(RedPacketRecordFields.PAYMENT_STATUS, Operator.EQ, Lists.newArrayList(RedPacketRecordObjFields.PaymentStatus.TRANSFERRING)),
                            SearchQueryUtil.filter(CommonFields.CREATE_TIME, Operator.GT, Lists.newArrayList(String.valueOf(queryTime))))
            );
            log.info("query2 :{}", JSON.toJSONString(query));
            CommonUtils.executeInAllDataWithFields(serviceFacade, User.systemUser(tenantId), RedPacketRecordFields.API_NAME, query, Lists.newArrayList(CommonFields.ID), (dataList) -> {
                log.info("data2 size:{}", dataList.size());
                dataList.forEach(data -> {
                    try {
                        rewardRecordSetter.setUpdateStatusTask(tenantId, RedPacketRecordFields.API_NAME, data.getId(), 0L, true, 1);
                    } catch (Exception e) {
                        log.info("rewardRecordSetter.setUpdateStatusTask err", e);
                    }
                });
            });
            log.info("self define redPacket done!");
        }).run();
        // 提现对象
        ParallelUtils.createParallelTask().submit(() -> refreshProcessingWithdraw(tenantId)).run();
    }

    @Override
    public void refreshProcessingWithdraw(String tenantId) {
        long leastTime = 1709111641450L;
        LocalDateTime lastMonth = LocalDateTime.now().minusMonths(3);
        long queryTime = Math.max(leastTime, lastMonth.toInstant(ZoneOffset.of("+8")).toEpochMilli());
        ParallelUtils.createParallelTask().submit(() -> {
            SearchTemplateQuery query = SearchQueryUtil.formSimpleQuery(0, -1,
                    Lists.newArrayList(SearchQueryUtil.filter(WithdrawRecordObjFields.PAYMENT_STATUS, Operator.EQ, Lists.newArrayList(WithdrawPaymentStatusEnum.PROCESSING.code())),
                            SearchQueryUtil.filter(WithdrawRecordObjFields.PAYMENT_ERROR_MESSAGE, Operator.IN, Lists.newArrayList("业务服务费余额不足","业务服务费余额不足，请充值后重试。")),
                            SearchQueryUtil.filter(CommonFields.CREATE_TIME, Operator.GT, Lists.newArrayList(String.valueOf(queryTime))))
            );
            log.info("query :{}", query);
            CommonUtils.executeInAllDataWithFields(serviceFacade, User.systemUser(tenantId), ApiNames.WITHDRAW_RECORD_OBJ, query, Lists.newArrayList(CommonFields.ID), (dataList) -> {
                log.info("data size:{}", dataList.size());
                dataList.forEach(data -> {
                    try {
                        redPacketWithdrawSetter.setUpdateStatusTask(tenantId, data.getId(), ApiNames.WITHDRAW_RECORD_OBJ, 0L);
                    } catch (Exception e) {
                        log.info("redPacketWithdrawSetter.setUpdateStatusTask err", e);
                    }
                });
            });
            log.info("preset withdraw done!");
        }).run();
    }

    @Override
    public String getTopTenantId(String tenantCode, String environment) {
        if (Strings.isNullOrEmpty(tenantCode)) {
            return tenantHierarchyService.findManufacturerByEnvironment(environment).getTenantId();
        }
        return TENANT_CODE_TO_TENANT_MAP.get(String.format("%s$%s", tenantCode, environment));
    }

    @Override
    public String getTenantCode(String tenantId) {
        if (TENANT_TO_TENANT_CODE_MAP.containsKey(tenantId)) {
            String value = TENANT_TO_TENANT_CODE_MAP.getOrDefault(tenantId, "");
            int index = value.indexOf("$");
            return index == -1 ? value : value.substring(0, index);
        }
        return "";
    }

    private void invalidRewardDetailByRedPacketDetail(String tenantId, List<IObjectData> redPacketDetail) {
        SearchTemplateQuery query = new SearchTemplateQuery();
        query.setLimit(0);
        query.setOffset(-1);

        Filter relatedApiNameFilter = new Filter();
        relatedApiNameFilter.setFieldName(TPMActivityRewardDetailFields.RELATED_OBJECT_API_NAME);
        relatedApiNameFilter.setOperator(Operator.EQ);
        relatedApiNameFilter.setFieldValues(Lists.newArrayList(ApiNames.RED_PACKET_RECORD_DETAIL_OBJ));

        Filter relatedDataIdFitler = new Filter();
        relatedDataIdFitler.setFieldName(TPMActivityRewardDetailFields.RELATED_OBJECT_DATA_ID);
        relatedDataIdFitler.setOperator(Operator.IN);
        relatedDataIdFitler.setFieldValues(redPacketDetail.stream().map(DBRecord::getId).collect(Collectors.toList()));

        query.setFilters(Lists.newArrayList(relatedDataIdFitler, relatedApiNameFilter));

        List<IObjectData> dataList = CommonUtils.queryData(serviceFacade, User.systemUser(tenantId), ApiNames.TPM_ACTIVITY_REWARD_DETAIL_OBJ, query);

        serviceFacade.bulkInvalid(dataList, User.systemUser(tenantId));
    }


    private boolean republish(IObjectData redPacket) {
        String lockKey = redPacket.getId();
        tryLock(lockKey);
        try {
            //重新获取一下数据
            redPacket = serviceFacade.findObjectData(User.systemUser(redPacket.getTenantId()), redPacket.getId(), ApiNames.RED_PACKET_RECORD_OBJ);
            String paymentBusinessId = redPacket.get(RedPacketRecordObjFields.TRADING_ID, String.class);
            String fromAccountType = redPacket.get(RedPacketRecordObjFields.TRANSFEROR_ACCOUNT_TYPE, String.class);
            String fromTransferAccount = redPacket.get(RedPacketRecordObjFields.TRANSFEROR_ACCOUNT, String.class);
            String fromTenantId = redPacket.get(RedPacketRecordObjFields.TRANSFEROR_TENANT_ID, String.class);
            if (Strings.isNullOrEmpty(fromTenantId)) {
                updateRewardRecordTransferInfo(fromTenantId, redPacket, null, null, RedPacketPaymentStatusEnum.EXCEPT.code(), "转出账户为空", null);
                return false;
            }
            String fromTenantAccount = String.valueOf(eieaConverter.enterpriseIdToAccount(Integer.parseInt(fromTenantId)));


            //查看当前的明细状态和记录状态的是否一致
            TransferDetail transferDetail = getTransferDetail(fromTenantId, fromAccountType, paymentBusinessId);

            if (transferDetail == null || TransferDetailStatusEnum.FAIL.codes().contains(transferDetail.getStatus())) {
                //确实失败了，重新发起
                Integer retryCount = redPacket.get(RedPacketRecordObjFields.RETRY_TIMES, BigDecimal.class, BigDecimal.ZERO).intValue();
                if (retryCount >= 3) {
                    log.info("retry count is more than 3. rewardRecord:{}", redPacket);
                    updateRewardRecordTransferInfo(fromTenantId, redPacket, null, null, RedPacketPaymentStatusEnum.EXCEPT.code(), null, null);
                    return false;
                }
                String newPaymentBusinessId = IdentityIdGenerator.formPaymentIdentityId();
                updateRewardRecordTransferInfo(fromTenantId, redPacket, newPaymentBusinessId, null, RedPacketPaymentStatusEnum.PROCESSING.code(), "", null);
                UserInfo userInfo = UserInfo.builder().tenantId(fromTenantId).userId("-10000").build();
                BigDecimal amount = redPacket.get(RedPacketRecordObjFields.REWARD_AMOUNT, BigDecimal.class);
                String remarks = redPacket.get(RedPacketRecordObjFields.REMARKS, String.class);
                if (RedPacketRecordObjFields.TransferorAccountType.CLOUD.equals(fromAccountType)) {
                    cloudTransfer(userInfo, fromTenantAccount, fromTenantId, fromTransferAccount, newPaymentBusinessId, amount, remarks, redPacket);
                } else if (RedPacketRecordObjFields.TransferorAccountType.WECHAT_MERCHANT.equals(fromAccountType)) {
                    wxTransfer(userInfo, fromTenantAccount, fromTenantId, fromTransferAccount, newPaymentBusinessId, amount, remarks, redPacket);
                } else {
                    log.info("异常转出账户类型：{}", fromAccountType);
                    updateRewardRecordTransferInfo(fromTenantId, redPacket, null, null, RedPacketPaymentStatusEnum.EXCEPT.code(), "异常转出账户类型。", null);
                }
            } else if (TransferDetailStatusEnum.SUCCESS.codes().contains(transferDetail.getStatus())) {
                //已经成功了，但是状态不对，更新状态
                updateRewardRecordTransferInfo(fromTenantId, redPacket, null, null, RedPacketPaymentStatusEnum.SUCCESS.code(), "", null);
            } else {
                return true;
            }
        } finally {
            unlock(lockKey);
        }
        return false;
    }

    private void wxTransfer(UserInfo userInfo, String fromTenantAccount, String fromTenantId, String transferAccount, String newPaymentBusinessId, BigDecimal amount, String remarks, IObjectData rewardRecord) {
        BatchWXTenantTransfer.Arg batchWXTenantTransferArg = new BatchWXTenantTransfer.Arg();
        batchWXTenantTransferArg.setBatchName(newPaymentBusinessId);
        batchWXTenantTransferArg.setBatchRemarks(newPaymentBusinessId);
        batchWXTenantTransferArg.setBatchTransferId(newPaymentBusinessId);
        WXTenantAccount wxTenantAccount = new WXTenantAccount();
        wxTenantAccount.setTenantAccount(fromTenantAccount);
        if (!"default".equals(transferAccount)) {
            wxTenantAccount.setAccount(transferAccount);
            WxMerchantAccountDTO merchantAccountDTO = JSON.parseObject(transferAccount, WxMerchantAccountDTO.class);
            wxTenantAccount.setSubMchId(merchantAccountDTO.getSubMchId());
        }
        batchWXTenantTransferArg.setPayeeWXAccount(wxTenantAccount);
        WXPersonalAccount wxPersonalAccount = new WXPersonalAccount();
        wxPersonalAccount.setAmount(amount);
        wxPersonalAccount.setOpenId(rewardRecord.get(RedPacketRecordObjFields.TRANSFEREE_WECHAT_OPEN_ID, String.class));
        wxPersonalAccount.setRemarks(remarks);
        wxPersonalAccount.setBusinessId(newPaymentBusinessId);
        wxPersonalAccount.setAppId(rewardRecord.get(RedPacketRecordObjFields.TRANSFEREE_WECHAT_APP_ID, String.class));
        batchWXTenantTransferArg.setReceiverAccounts(Lists.newArrayList(wxPersonalAccount));

        BatchWXTenantTransfer.Result queryResult;
        try {
            queryResult = payService.batchWXTenantTransfer(userInfo, batchWXTenantTransferArg);
        } catch (Exception e) {
            String message = e.getMessage();
            if (!(e instanceof RewardFmcgException)) {
                message = "微信转账发生未知异常";
            }
            updateRewardRecordTransferInfo(fromTenantId, rewardRecord, newPaymentBusinessId, null, RedPacketPaymentStatusEnum.FAIL.code(), message, rewardRecord.get(RedPacketRecordObjFields.RETRY_TIMES, BigDecimal.class, BigDecimal.ZERO).intValue() + 1);
            throw e;
        }
        updateRewardRecordTransferInfo(rewardRecord.getTenantId(), rewardRecord, queryResult.getBatchTransferId(), queryResult.getDetailResults().get(0).getTransferId(), RedPacketPaymentStatusEnum.PROCESSING.code(), "", null);
        rewardRuleRedPacketSetter.setUpdateStatusTask(rewardRecord.getTenantId(), rewardRecord.getId());
    }

    private void cloudTransfer(UserInfo userInfo, String fromTenantAccount, String fromTenantId, String transferAccount, String newPaymentBusinessId, BigDecimal amount, String remarks, IObjectData rewardRecord) {
        CloudTransfer.Arg transferArg = new CloudTransfer.Arg();
        transferArg.setBusinessId(newPaymentBusinessId);
        transferArg.setAmount(amount);
        transferArg.setRemarks(remarks);
        String realName = rewardRecord.get(RedPacketRecordObjFields.TRANSFEREE_NAME, String.class);
        String idCard = rewardRecord.get(RedPacketRecordObjFields.TRANSFEREE_ID, String.class);
        String phone = rewardRecord.get(RedPacketRecordObjFields.TRANSFEREE_PHONE, String.class);
        String openId = rewardRecord.get(RedPacketRecordObjFields.TRANSFEREE_WECHAT_OPEN_ID, String.class);
        String wxAppId = rewardRecord.get(RedPacketRecordObjFields.TRANSFEREE_WECHAT_APP_ID, String.class);
        CloudAccount account = new CloudAccount();
        account.setTenantAccount(fromTenantAccount);
        if (!"default".equals(transferAccount)) {
//            account.setCloudAccountDealerId(transferAccount);
            try {
                JSONObject transferAccountJson = JSONObject.parseObject(transferAccount);
                account.setCloudAccountDealerId(transferAccountJson.getString("dealerId"));
                account.setCloudAccountBrokerId(transferAccountJson.getString("brokerId"));
            } catch (Exception exception) {
                log.error("transferAccount error is ", exception);
            }
        }
        transferArg.setPayerCloudAccount(account);
        WXCloudPayReceiverAccount wxCloudPayReceiverAccount = new WXCloudPayReceiverAccount(realName, idCard, phone, openId);
        wxCloudPayReceiverAccount.setAppId(wxAppId);
        transferArg.setReceiverPayAccount(wxCloudPayReceiverAccount);
        CloudTransfer.Result transferResult;
        try {
            transferResult = payService.cloudTransfer(userInfo, transferArg);
        } catch (Exception e) {
            String message = e.getMessage();
            if (!(e instanceof RewardFmcgException)) {
                message = "云账户转账发生未知异常";
            }
            updateRewardRecordTransferInfo(fromTenantId, rewardRecord, newPaymentBusinessId, null, RedPacketPaymentStatusEnum.FAIL.code(), message, rewardRecord.get(RedPacketRecordObjFields.RETRY_TIMES, BigDecimal.class, BigDecimal.ZERO).intValue() + 1);
            throw e;
        }
        updateRewardRecordTransferInfo(rewardRecord.getTenantId(), rewardRecord, transferResult.getBusinessId(), transferResult.getTransferId(), RedPacketPaymentStatusEnum.PROCESSING.code(), "", null);
        //publish a new task to update status
        rewardRuleRedPacketSetter.setUpdateStatusTask(rewardRecord.getTenantId(), rewardRecord.getId());
    }

    private boolean queryRecordStatus(IObjectData redPacket) {
        String paymentBusinessId = redPacket.get(RedPacketRecordObjFields.TRADING_ID, String.class);
        String fromAccountType = redPacket.get(RedPacketRecordObjFields.TRANSFEROR_ACCOUNT_TYPE, String.class);
        String fromTenantId = redPacket.get(RedPacketRecordObjFields.TRANSFEROR_TENANT_ID, String.class);
        String fromTenantAccount = String.valueOf(eieaConverter.enterpriseIdToAccount(Integer.parseInt(fromTenantId)));
        TransferDetail transferDetail = getTransferDetail(fromTenantId, fromAccountType, paymentBusinessId);
        if (transferDetail == null) {
            log.info("transfer detail is null. paymentBusinessId:{}", paymentBusinessId);
            updateRewardRecordTransferInfo(fromTenantId, redPacket, null, null, RedPacketPaymentStatusEnum.PUBLISH_FAIL.code(), "转账发起失败", null);
            return true;
        } else if (TransferDetailStatusEnum.FAIL.codes().contains(transferDetail.getStatus())
                || TransferDetailStatusEnum.CANCEL.codes().contains(transferDetail.getStatus())) {
            if (transferDetail.getStatus().equals("15")){
                transferDetail.setFailReason("取消支付");
            }
            log.info("transfer fail. transferDetail:{}", transferDetail);
            updateRewardRecordTransferInfo(fromTenantId, redPacket, null, null, RedPacketPaymentStatusEnum.FAIL.code(), Strings.isNullOrEmpty(transferDetail.getFailReason()) ? "未知转账失败原因" : transferDetail.getFailReason(), redPacket.get(RedPacketRecordObjFields.RETRY_TIMES, BigDecimal.class, BigDecimal.ZERO).intValue() + 1);
            return true;
        } else if (TransferDetailStatusEnum.SUCCESS.codes().contains(transferDetail.getStatus())) {
            updateRewardRecordTransferInfo(fromTenantId, redPacket, null, null, RedPacketPaymentStatusEnum.SUCCESS.code(), "", null);
            fillTransferInfo(fromTenantId, redPacket, transferDetail);
        } else if (NEED_REASON_STATUS.contains(transferDetail.getStatus())) {
            log.info("order status is always processing, due to have some operate to do . transferDetail:{}", transferDetail);
            updateRewardRecordTransferInfo(fromTenantId, redPacket, null, null, RedPacketPaymentStatusEnum.PROCESSING.code(), transferDetail.getFailReason(), 3);
        } else {
            log.info("transfer processing. transferDetail:{}", transferDetail);
            return true;
        }
        return false;
    }

    private void fillTransferInfo(String fromTenantId, IObjectData redPacket, TransferDetail transferDetail) {
        try{
            if (!Strings.isNullOrEmpty(transferDetail.getBrokerId())) {
                Map<String, Object> updateMap = new HashMap<>();
                updateMap.put(RedPacketRecordObjFields.FROM_CLOUD_ACCOUNT_BROKER_ID, transferDetail.getBrokerId());
                updateMap.put(RedPacketRecordObjFields.FROM_CLOUD_ACCOUNT_DEALER_ID, transferDetail.getDealerId());
                serviceFacade.updateWithMap(User.systemUser(redPacket.getTenantId()), redPacket, updateMap);
            } else if (!Strings.isNullOrEmpty(transferDetail.getSubMchId())) {
                Map<String, Object> updateMap = new HashMap<>();
                updateMap.put(RedPacketRecordObjFields.FROM_WX_SUB_MCH_ID, transferDetail.getSubMchId());
                serviceFacade.updateWithMap(User.systemUser(redPacket.getTenantId()), redPacket, updateMap);
            }
        }catch (Exception e){
            log.info("fillTransferInfo err,",e);
        }
    }

    private TransferDetail getTransferDetail(String fromTenantId, String fromAccountType, String paymentBusinessId) {
        TransferDetail transferDetail = null;
        if (RedPacketRecordObjFields.TransferorAccountType.WECHAT_MERCHANT.equals(fromAccountType)) {
            transferDetail = getWxTransferDetail(fromTenantId, paymentBusinessId);
        } else if (RedPacketRecordObjFields.TransferorAccountType.CLOUD.equals(fromAccountType)) {
            transferDetail = getCloudTransferDetail(fromTenantId, paymentBusinessId);
        } else {
            log.info("暂不支持的账户类型:{}", fromAccountType);
        }
        return transferDetail;
    }

    private TransferDetail getWxTransferDetail(String tenantId, String businessId) {
        QueryWXTenantTransferDetail.Arg batchQueryWXTenantTransferDetailArg = new QueryWXTenantTransferDetail.Arg();
        batchQueryWXTenantTransferDetailArg.setBatchTransferId(businessId);
        batchQueryWXTenantTransferDetailArg.setBusinessId(businessId);
        UserInfo userInfo = UserInfo.builder().userId("-10000").tenantId(tenantId).build();
        QueryWXTenantTransferDetail.Result queryResult = payService.queryWXTenantTransferDetails(userInfo, batchQueryWXTenantTransferDetailArg);
        return CollectionUtils.isNotEmpty(queryResult.getTransferDetails()) ? queryResult.getTransferDetails().get(0) : null;
    }

    private TransferDetail getCloudTransferDetail(String tenantId, String businessId) {
        QueryCloudTransferDetails.Arg arg = new QueryCloudTransferDetails.Arg();
        arg.setBusinessId(businessId);
        UserInfo userInfo = UserInfo.builder().userId("-10000").tenantId(tenantId).build();
        QueryCloudTransferDetails.Result result = payService.queryCloudTransferDetails(userInfo, arg);
        return CollectionUtils.isNotEmpty(result.getTransferDetails()) ? result.getTransferDetails().get(0) : null;
    }

    public void updateRewardRecordTransferInfo(String tenantId, IObjectData redPacketRecord, String businessId, String orderId, String status, String failMessage, Integer retryCount) {
        Map<String, Object> updateMap = new HashMap<>();
        Map<String, Object> activityRewardUpdateMap = new HashMap<>();
        String updateMessage = "";
        if (businessId != null) {
            updateMap.put(RedPacketRecordObjFields.TRADING_ID, businessId);
            updateMessage += fromUpdateMessage(RedPacketRecordObjFields.TRADING_ID, businessId);
        }
        if (orderId != null) {
            updateMap.put(RedPacketRecordObjFields.ORDER_ID, orderId);
            updateMessage += fromUpdateMessage(RedPacketRecordObjFields.ORDER_ID, orderId);
        }
        if (status != null) {
            updateMap.put(RedPacketRecordObjFields.PAYMENT_STATUS, status);
            updateMessage += fromUpdateMessage(RedPacketRecordObjFields.PAYMENT_STATUS, status);
            if (status.equals(RedPacketRecordObjFields.PaymentStatus.SUCCESS)) {
                activityRewardUpdateMap.put(TPMActivityRewardDetailFields.STATUS, TPMActivityRewardDetailFields.Status.DONE);
            } else if (status.equals(RedPacketRecordObjFields.PaymentStatus.ERROR) || status.equals(RedPacketRecordObjFields.PaymentStatus.FAIL)) {
                activityRewardUpdateMap.put(TPMActivityRewardDetailFields.STATUS, TPMActivityRewardDetailFields.Status.ERROR);
            } else {
                activityRewardUpdateMap.put(TPMActivityRewardDetailFields.STATUS, TPMActivityRewardDetailFields.Status.UNDO);
            }
        }
        if (failMessage != null) {
            updateMap.put(RedPacketRecordObjFields.PAYMENT_ERROR_MESSAGE, failMessage);
            activityRewardUpdateMap.put(TPMActivityRewardDetailFields.DISTRIBUTION_INSTRUCTIONS, failMessage);
            if (failMessage.isEmpty()) {
                failMessage = "空";
            }
            updateMessage += fromUpdateMessage(RedPacketRecordObjFields.PAYMENT_ERROR_MESSAGE, failMessage);
        }
        if (retryCount != null) {
            updateMap.put(RedPacketRecordObjFields.RETRY_TIMES, retryCount);
            updateMessage += fromUpdateMessage(RedPacketRecordObjFields.RETRY_TIMES, String.valueOf(retryCount));
        }
        if (updateMap.isEmpty()) {
            return;
        }
        User sysUser = User.systemUser(redPacketRecord.getTenantId());
        serviceFacade.updateWithMap(sysUser, redPacketRecord, updateMap);
        IObjectDescribe objectDescribe = serviceFacade.findObject(redPacketRecord.getTenantId(), redPacketRecord.getDescribeApiName());
        serviceFacade.logWithCustomMessage(sysUser, EventType.MODIFY, ActionType.Modify, objectDescribe, redPacketRecord, updateMessage.substring(0, updateMessage.length() - 1));

        if (!activityRewardUpdateMap.isEmpty()) {
            List<IObjectData> rewardDetails = getActivityRewardDetailByRelatedObject(redPacketRecord.getTenantId(), redPacketRecord.getId());
            if (CollectionUtils.isNotEmpty(rewardDetails)) {
                serviceFacade.batchUpdateWithMap(sysUser, rewardDetails, activityRewardUpdateMap);
            }
        }
    }

    private List<IObjectData> getActivityRewardDetailByRelatedObject(String tenantId, String relatedObjectId) {

        SearchTemplateQuery query = new SearchTemplateQuery();
        query.setLimit(-1);
        query.setOffset(0);

        Filter redPacketFilter = new Filter();
        redPacketFilter.setFieldName(RedPacketRecordDetailObjFields.RED_PACKET_RECORD_ID);
        redPacketFilter.setOperator(Operator.EQ);
        redPacketFilter.setFieldValues(Lists.newArrayList(relatedObjectId));

        query.setFilters(Lists.newArrayList(redPacketFilter));

        List<IObjectData> dataList = CommonUtils.queryData(serviceFacade, User.systemUser(tenantId), ApiNames.RED_PACKET_RECORD_DETAIL_OBJ, query, Lists.newArrayList(CommonFields.ID));

        if (CollectionUtils.isEmpty(dataList)) {
            return dataList;
        }

        SearchTemplateQuery rewardDetailQuery = new SearchTemplateQuery();
        rewardDetailQuery.setLimit(-1);
        rewardDetailQuery.setOffset(0);

        Filter apiNameFilter = new Filter();
        apiNameFilter.setFieldName(TPMActivityRewardDetailFields.RELATED_OBJECT_API_NAME);
        apiNameFilter.setOperator(Operator.EQ);
        apiNameFilter.setFieldValues(Lists.newArrayList(ApiNames.RED_PACKET_RECORD_DETAIL_OBJ));

        Filter dataIdFilter = new Filter();
        dataIdFilter.setFieldName(TPMActivityRewardDetailFields.RELATED_OBJECT_DATA_ID);
        dataIdFilter.setOperator(Operator.IN);
        dataIdFilter.setFieldValues(dataList.stream().map(DBRecord::getId).collect(Collectors.toList()));

        rewardDetailQuery.setFilters(Lists.newArrayList(apiNameFilter, dataIdFilter));


        return CommonUtils.queryData(serviceFacade, User.systemUser(tenantId), ApiNames.TPM_ACTIVITY_REWARD_DETAIL_OBJ, rewardDetailQuery);
    }

    private String fromUpdateMessage(String apiName, String value) {
        return String.format("字段【%s】 变更为  %s ，", apiName, value);
    }


    private void tryLock(String key) {
        RLock lock = redissonCmd.getLock(key);
        try {
            if (!lock.tryLock(20000, TimeUnit.MILLISECONDS)) {
                throw new RewardFmcgException("20001", I18N.text(I18NKeys.REWARD2_RED_PACKET_SERVICE_0));
            }
        } catch (InterruptedException e) {
            throw new RewardFmcgException("20001", I18N.text(I18NKeys.REWARD2_RED_PACKET_SERVICE_1));
        }
    }

    private void unlock(String key) {
        RLock lock = redissonCmd.getLock(key);
        if (lock.isLocked() && lock.isHeldByCurrentThread()) {
            lock.unlock();
        }
    }
}
