package com.facishare.crm.fmcg.tpm.dao.mongo.po;

import java.util.Map;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * description : just code
 * <p>
 * create by @yangqf
 * create time 2021/11/17 19:02
 */
public enum BudgetMethodEnum {

    AUTOMATIC("automatic"),
    MANUAL("manual");

    private static final Map<String, BudgetMethodEnum> innerMap = Stream.of(values()).collect(Collectors.toMap(BudgetMethodEnum::value, v -> v, (before, after) -> before));

    BudgetMethodEnum(String value) {
        this.value = value;
    }

    private final String value;

    public String value() {
        return this.value;
    }

    public static BudgetMethodEnum of(String value) {
        return innerMap.get(value);
    }
}
