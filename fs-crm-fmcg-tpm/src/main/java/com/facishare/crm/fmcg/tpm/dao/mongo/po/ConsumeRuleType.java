package com.facishare.crm.fmcg.tpm.dao.mongo.po;

import java.util.Map;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @date 2022/8/2 上午11:53
 */
public enum ConsumeRuleType {

    FREEZE_DEDUCTION("freeze_deduction"),
    DEDUCTION("deduction"),
    FREEZE("freeze"),
    RELEASE("release");

    private static final Map<String, ConsumeRuleType> innerMap = Stream.of(values()).collect(Collectors.toMap(ConsumeRuleType::value, v -> v, (before, after) -> before));

    ConsumeRuleType(String value) {
        this.value = value;
    }

    private final String value;

    public String value() {
        return this.value;
    }

    public static ConsumeRuleType of(String value) {
        return innerMap.get(value);
    }
}
