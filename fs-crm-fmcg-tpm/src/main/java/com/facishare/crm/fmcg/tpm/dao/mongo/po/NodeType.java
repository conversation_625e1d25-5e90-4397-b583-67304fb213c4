package com.facishare.crm.fmcg.tpm.dao.mongo.po;

import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.crm.fmcg.tpm.utils.I18NKeys;
import com.facishare.paas.I18N;

import java.util.Map;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * description : just code
 * <p>
 * create by @yangqf
 * create time 2021/11/17 19:02
 */
public enum NodeType {
    PLAN_TEMPLATE("plan_template", 0),
    PLAN("plan", 1),
    AGREEMENT("agreement", 2),
    PROOF("proof", 3),
    AUDIT("audit", 4),
    COST_ASSIGN("cost_assign", 5),
    STORE_WRITE_OFF("store_write_off", 6),
    WRITE_OFF("write_off", 7),
    CUSTOM("custom", 9);

    private static final Map<String, NodeType> valueMap = Stream.of(NodeType.values()).collect(Collectors.toMap(NodeType::value, v -> v, (before, after) -> before));

    NodeType(String value, int order) {
        this.value = value;
        this.order = order;
    }

    private final String value;
    private final int order;

    public String value() {
        return this.value;
    }

    public int order() {
        return this.order;
    }

    public static NodeType of(String value) {
        if (valueMap.containsKey(value))
            return valueMap.get(value);
        else
            throw new ValidateException(I18N.text(I18NKeys.NODE_TYPE_0) + value);
    }

    public static int getOrder(String value) {
        if (valueMap.containsKey(value))
            return valueMap.get(value).order;
        else {
            throw new ValidateException(I18N.text(I18NKeys.NODE_TYPE_1) + value);
        }
    }
}
