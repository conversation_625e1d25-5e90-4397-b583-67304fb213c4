package com.facishare.crm.fmcg.tpm.dao.mongo.po;

import lombok.Data;
import lombok.ToString;
import org.mongodb.morphia.annotations.Property;

import java.io.Serializable;

@Data
@ToString
public class OuterConsumerRewardRuleEntity implements Serializable {

    public static final String F_ID = "id";
    public static final String F_PLATFORM = "platform";

    @Property(F_ID)
    private String id;

    @Property(F_PLATFORM)
    private String platform;
}