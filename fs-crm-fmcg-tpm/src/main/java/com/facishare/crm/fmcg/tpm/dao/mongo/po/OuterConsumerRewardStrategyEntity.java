package com.facishare.crm.fmcg.tpm.dao.mongo.po;

import lombok.Data;
import lombok.ToString;
import org.mongodb.morphia.annotations.Embedded;

import java.io.Serializable;
import java.util.List;

@Data
@ToString
public class OuterConsumerRewardStrategyEntity implements Serializable {

    public static final String F_OUTER_CONSUMER_REWARD_RULES = "outer_consumer_reward_rules";

    @Embedded(F_OUTER_CONSUMER_REWARD_RULES)
    private List<OuterConsumerRewardRuleEntity> outerConsumerRewardRules;
}