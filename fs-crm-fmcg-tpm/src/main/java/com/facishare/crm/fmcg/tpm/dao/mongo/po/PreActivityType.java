package com.facishare.crm.fmcg.tpm.dao.mongo.po;

import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.crm.fmcg.tpm.utils.I18NKeys;
import com.facishare.paas.I18N;

import java.util.Map;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * author: wuyx
 * description: 预置活动类型
 * createTime: 2022/3/17 15:52
 */
public enum PreActivityType {

    TYPE_ACTIVITY("type_activity__c", 1),
    TYPE_ACTIVITY_LIVE("type_activity_live__c", 2),

    TYPE_ACTIVITY_SCAN_CODE_GET_REWARD("crmat_code_reward__c", 3),//码上有礼

    TYPE_ACTIVITY_STOCK_UP_REWARD("type_stock_up_reward__c", 4),//囤货激励

    TYPE_ACTIVITY_BIG_DATE("crmat_big_date__c", 5);//临期减价处理

    private static final Map<String, PreActivityType> valueMap = Stream.of(PreActivityType.values()).collect(Collectors.toMap(PreActivityType::apiName, v -> v, (before, after) -> before));
    private static final Map<Integer, PreActivityType> orderMap = Stream.of(PreActivityType.values()).collect(Collectors.toMap(PreActivityType::order, v -> v, (before, after) -> before));

    PreActivityType(String apiName, int order) {
        this.apiName = apiName;
        this.order = order;
    }

    private final String apiName;
    private final int order;

    public String apiName() {
        return this.apiName;
    }

    public int order() {
        return this.order;
    }

    public static PreActivityType of(String value) {
        if (valueMap.containsKey(value)) {
            return valueMap.get(value);
        } else {
            throw new ValidateException(I18N.text(I18NKeys.PRE_ACTIVITY_TYPE_0) + value);
        }
    }

    public static PreActivityType of(int order) {
        if (orderMap.containsKey(order)) {
            return orderMap.get(order);
        } else {
            throw new ValidateException(I18N.text(I18NKeys.PRE_ACTIVITY_TYPE_1) + order);
        }
    }
}
