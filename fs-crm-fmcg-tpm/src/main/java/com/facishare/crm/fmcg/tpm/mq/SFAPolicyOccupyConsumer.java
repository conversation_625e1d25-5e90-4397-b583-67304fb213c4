package com.facishare.crm.fmcg.tpm.mq;

import com.alibaba.fastjson.JSON;
import com.facishare.crm.fmcg.common.adapter.dto.exception.EventAbandonException;
import com.facishare.crm.fmcg.tpm.web.contract.StatisticPolicyOccupy;
import com.facishare.crm.fmcg.tpm.web.service.abstraction.IPromotionPolicyService;
import com.fxiaoke.rocketmq.consumer.AutoConfMQPushConsumer;
import com.github.trace.TraceContext;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyStatus;
import org.apache.rocketmq.client.consumer.listener.MessageListenerConcurrently;
import org.apache.rocketmq.common.message.MessageExt;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.ApplicationListener;
import org.springframework.context.event.ContextRefreshedEvent;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import javax.annotation.Resource;

/**
 * author: wuyx
 * description: 价格政策统计
 * createTime: 2023/9/5 11:45
 */
@Component
@Slf4j
public class SFAPolicyOccupyConsumer implements ApplicationListener<ContextRefreshedEvent> {

    private static final Logger logger = LoggerFactory.getLogger(SFAPolicyOccupyConsumer.class);

    private static final String CONFIG_NAME = "fs-fmcg-framework-config";
    private static final String NAME_SERVER_KEY = "SFA_POLICY_OCCUPY_NAMESERVER";
    private static final String TOPIC_KEY = "SFA_POLICY_OCCUPY_TOPICS";
    private static final String GROUP_NAME = "SFA_POLICY_OCCUPY_GROUP";

    private AutoConfMQPushConsumer consumer;

    @Resource
    private IPromotionPolicyService promotionPolicyService;

    @PostConstruct
    public void init() {
        logger.info("SFAPolicyOccupyConsumer start init.");
        MessageListenerConcurrently listener = (messages, context) -> {

            boolean reconsume = false;
            for (MessageExt messageExt : messages) {
                try {
                    TraceContext.get().setTraceId(String.format("SFA_POLICY_OCCUPY_MQ.%s.%s", messageExt.getMsgId(), messageExt.getReconsumeTimes()));
                    process(messageExt);
                } catch (EventAbandonException ex) {
                    logger.warn("[SFAPolicyOccupyConsumer message abandon : ", ex);
                } catch (Exception ex) {
                    logger.error("[SFAPolicyOccupyConsumer consumer error :" + messages + "]", ex);
                    reconsume = true;
                } finally {
                    TraceContext.remove();
                }
            }
            if (reconsume) {
                return ConsumeConcurrentlyStatus.RECONSUME_LATER;
            }
            return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
        };
        try {
            consumer = new AutoConfMQPushConsumer(CONFIG_NAME, listener);
            consumer.setConsumeTopicKey(TOPIC_KEY);
            consumer.setNameServerKey(NAME_SERVER_KEY);
            consumer.setGroupNameKey(GROUP_NAME);
        } catch (Exception e) {
            logger.error("init SFAPolicyOccupyConsumer mq consumer failed.", e);
        }
    }

    private void process(MessageExt messageExt) {
        logger.info("original message : {}", messageExt);

        StatisticPolicyOccupy.Arg arg;
        try {
            arg = JSON.parseObject(messageExt.getBody(), StatisticPolicyOccupy.Arg.class);
        } catch (Exception ex) {
            throw new EventAbandonException("parse message body cause unknown exception.");
        }

        logger.info("statistic policy occupy start : {}", arg);
        promotionPolicyService.statisticPolicyOccupy(arg);
    }

    @Override
    public void onApplicationEvent(ContextRefreshedEvent event) {
        if (consumer != null && event.getApplicationContext().getParent() == null) {
            consumer.start();
            log.info("SFAPolicyOccupyConsumer consumer started.");
        }
    }

    @PreDestroy
    public void shutDown() {
        consumer.shutdown();
    }
}
