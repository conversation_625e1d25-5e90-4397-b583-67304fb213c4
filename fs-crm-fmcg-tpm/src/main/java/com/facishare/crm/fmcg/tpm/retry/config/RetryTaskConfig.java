package com.facishare.crm.fmcg.tpm.retry.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import com.google.common.util.concurrent.ThreadFactoryBuilder;

import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

@Configuration
public class RetryTaskConfig {
    public static final String TASK_QUEUE_PREFIX = "fmcg_retry_task_queue_";
    public static final long RETRY_INTERVAL = 5000L;
    public static final int DAILY_CHECK_INITIAL_DELAY = 1;
    public static final int DEFAULT_RATE_LIMIT = 10;
    
    @Bean
    public ThreadPoolExecutor retryTaskExecutor() {
        return new ThreadPoolExecutor(
            10,
            20,
            60L,
            TimeUnit.SECONDS,
            new LinkedBlockingQueue<>(1000),
            new ThreadFactoryBuilder()
                .setNameFormat("retry-task-pool-%d")
                .build(),
            new ThreadPoolExecutor.CallerRunsPolicy()
        );
    }
}