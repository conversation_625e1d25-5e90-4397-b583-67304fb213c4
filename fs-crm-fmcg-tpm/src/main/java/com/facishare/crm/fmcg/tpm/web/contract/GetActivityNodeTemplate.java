package com.facishare.crm.fmcg.tpm.web.contract;

import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.crm.fmcg.tpm.web.contract.model.ActivityNodeTemplateVO;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.Builder;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;

/**
 * description : just code
 * <p>
 * create by @yangqf
 * create time 2021/11/15 17:19
 */
public interface GetActivityNodeTemplate {

    @Data
    @ToString
    class Arg implements Serializable {

        @JSONField(name = "_id")
        @JsonProperty(value = "_id")
        @SerializedName("_id")
        private String id;
    }


    @Data
    @ToString
    @Builder
    class Result implements Serializable {

        @JSONField(name = "activity_node_template")
        @JsonProperty(value = "activity_node_template")
        @SerializedName("activity_node_template")
        private ActivityNodeTemplateVO activityNodeTemplate;
    }
}