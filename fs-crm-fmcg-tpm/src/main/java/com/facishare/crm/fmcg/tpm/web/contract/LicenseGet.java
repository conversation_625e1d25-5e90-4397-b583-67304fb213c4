package com.facishare.crm.fmcg.tpm.web.contract;

import lombok.Builder;
import lombok.Data;

import java.io.Serializable;
import java.util.Map;
import java.util.Set;

public interface LicenseGet {
    @Data
    class Arg implements Serializable {
        private Set<String> codes;

    }

    @Builder
    @Data
    class Result implements Serializable {
        private Map<String, Boolean> licenseStatus;
    }
}
