package com.facishare.crm.fmcg.tpm.web.contract;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.Builder;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;

/**
 * description : just code
 * <p>
 * create by @wuyx
 * create time 2022/6/2 3:50
 */
public interface SalesOrderUpdateField {

    @Data
    @ToString
    class Arg implements Serializable {

        @JSONField(name = "activity_id")
        @JsonProperty(value = "activity_id")
        @SerializedName("activity_id")
        private String activityId;

        @JSONField(name = "field_api_name")
        @JsonProperty(value = "field_api_name")
        @SerializedName("field_api_name")
        private String fieldApiName;

        @JSONField(name = "value")
        @JsonProperty(value = "value")
        @SerializedName("value")
        private String value;

    }

    @Data
    @ToString
    @Builder
    class Result implements Serializable {

        @JSONField(name = "field_api_name")
        @JsonProperty(value = "field_api_name")
        @SerializedName("field_api_name")
        private String fieldApiName;

        @JSONField(name = "value")
        @JsonProperty(value = "value")
        @SerializedName("value")
        private String value;
    }
}