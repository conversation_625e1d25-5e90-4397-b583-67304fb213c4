package com.facishare.crm.fmcg.tpm.web.contract;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.util.List;

public interface StoreWriteOffCashingProductData {

    @Data
    @ToString
    class Arg implements Serializable {

        @SerializedName("data")
        @JSONField(name = "data")
        @JsonProperty("data")
        private JSONObject data;

        // 更新的数据
        @SerializedName("cashing_product_data")
        @JSONField(name = "cashing_product_data")
        @JsonProperty("cashing_product_data")
        private List<JSONObject> cashingProductData;
    }

    @Data
    @ToString
    class Result implements Serializable {

        @SerializedName("store_write_off_cashing_product_data")
        @JSONField(name = "store_write_off_cashing_product_data")
        @JsonProperty("store_write_off_cashing_product_data")
        private List<ObjectDataDocument> storeWriteOffCashingProductData;
    }

}
