package com.facishare.crm.fmcg.tpm.web.contract;

import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.crm.fmcg.tpm.web.contract.model.ValidActivityTypeVO;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.Builder;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.util.List;

/**
 * description : just code
 * <p>
 * create by @yangqf
 * create time 2021/11/17 17:36
 */
public interface ValidActivityType {

    @Data
    @ToString
    class Arg implements Serializable {

        @JSONField(name = "object_api_name")
        @JsonProperty(value = "object_api_name")
        @SerializedName("object_api_name")
        private String objectApiName;
    }

    @Data
    @ToString
    @Builder
    class Result implements Serializable {

        private List<ValidActivityTypeVO> data;

        private List<String> dealerRecordType;

        private String dealerApiName;
    }
}
