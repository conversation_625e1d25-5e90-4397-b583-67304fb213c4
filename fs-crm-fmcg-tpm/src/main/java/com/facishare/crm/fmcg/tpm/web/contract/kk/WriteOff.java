package com.facishare.crm.fmcg.tpm.web.contract.kk;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.Builder;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * description : just code
 * <p>
 * create by @yangqf
 * create time 2022/11/14 11:38
 */
public interface WriteOff {

    @Data
    @ToString
    class Arg implements Serializable {

        /**
         * 业务对象的 api name
         */
        @JSONField(name = "write_off_object_api_name")
        @JsonProperty(value = "write_off_object_api_name")
        @SerializedName("write_off_object_api_name")
        private String writeOffObjectApiName;

        /**
         * 业务对象的 id
         */
        @JSONField(name = "write_off_object_data_id")
        @JsonProperty(value = "write_off_object_data_id")
        @SerializedName("write_off_object_data_id")
        private String writeOffObjectDataId;

        /**
         * 业务对象的 api name
         */
        @JSONField(name = "api_name")
        @JsonProperty(value = "api_name")
        @SerializedName("api_name")
        private String apiName;

        /**
         * 业务对象的 id
         */
        @JSONField(name = "data_id")
        @JsonProperty(value = "data_id")
        @SerializedName("data_id")
        private String dataId;

        @JSONField(name = "write_off_data")
        @JsonProperty(value = "write_off_data")
        @SerializedName("write_off_data")
        private List<WriteOffItemVO> writeOffData;
    }

    @Data
    @ToString
    class WriteOffItemVO implements Serializable {

        @JSONField(name = "account_id")
        @JsonProperty(value = "account_id")
        @SerializedName("account_id")
        private String accountId;

        @JSONField(name = "amount")
        @JsonProperty(value = "amount")
        @SerializedName("amount")
        private BigDecimal amount;
    }

    @Data
    @ToString
    @Builder
    class Result implements Serializable {

        @JSONField(name = "business_trace_id")
        @JsonProperty(value = "business_trace_id")
        @SerializedName("business_trace_id")
        private String businessTraceId;

        @JSONField(name = "approval_trace_id")
        @JsonProperty(value = "approval_trace_id")
        @SerializedName("approval_trace_id")
        private String approvalTraceId;
    }
}