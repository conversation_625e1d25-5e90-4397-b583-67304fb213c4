package com.facishare.crm.fmcg.tpm.web.contract.model;

import org.springframework.util.CollectionUtils;
import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.crm.fmcg.tpm.dao.mongo.po.ActivityProofFrequencyConfigEntity;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.common.collect.Lists;
import com.google.gson.annotations.SerializedName;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.util.List;
import java.util.stream.Collectors;

/**
 * description : just code
 * <p>
 * create by @yangqf
 * create time 2021/12/6 15:17
 */
@Data
@ToString
public class ActivityProofFrequencyConfigVO implements Serializable {

    @JSONField(name = "frequency_type")
    @JsonProperty(value = "frequency_type")
    @SerializedName("frequency_type")
    private String frequencyType;

    @JSONField(name = "frequency_limit")
    @JsonProperty(value = "frequency_limit")
    @SerializedName("frequency_limit")
    private String frequencyLimit;

    @JSONField(name = "limit_days")
    @JsonProperty(value = "limit_days")
    @SerializedName("limit_days")
    private List<LimitSpanVO> limitDays;

    @JSONField(name = "limit_hours")
    @JsonProperty(value = "limit_hours")
    @SerializedName("limit_hours")
    private List<LimitSpanVO> limitHours;

    public static ActivityProofFrequencyConfigVO fromPO(ActivityProofFrequencyConfigEntity po) {
        if (po == null) {
            return null;
        }
        ActivityProofFrequencyConfigVO vo = new ActivityProofFrequencyConfigVO();
        vo.setFrequencyType(po.getFrequencyType());
        vo.setFrequencyLimit(po.getFrequencyLimit());
        if (CollectionUtils.isEmpty(po.getLimitDays())) {
            vo.setLimitDays(Lists.newArrayList());
        } else {
            vo.setLimitDays(po.getLimitDays().stream().map(LimitSpanVO::fromPO).collect(Collectors.toList()));
        }
        if (CollectionUtils.isEmpty(po.getLimitHours())) {
            vo.setLimitHours(Lists.newArrayList());
        } else {
            vo.setLimitHours(po.getLimitHours().stream().map(LimitSpanVO::fromPO).collect(Collectors.toList()));
        }
        return vo;
    }
}
