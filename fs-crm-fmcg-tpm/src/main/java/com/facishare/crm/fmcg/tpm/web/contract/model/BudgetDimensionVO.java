package com.facishare.crm.fmcg.tpm.web.contract.model;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;

/**
 * description : just code
 * <p>
 * create by @yangqf
 * create time 2022/7/13 09:46
 */
@Data
@ToString
public class BudgetDimensionVO implements Serializable {

    @JSONField(name = "api_name")
    @JsonProperty(value = "api_name")
    @SerializedName("api_name")
    private String apiName;

    @JSONField(name = "type")
    @JsonProperty(value = "type")
    @SerializedName("type")
    private String type;

    @JSONField(name = "label")
    @JsonProperty(value = "label")
    @SerializedName("label")
    private String label;

    @JSONField(name = "level")
    @JsonProperty(value = "level")
    @SerializedName("level")
    private Integer level;
}
