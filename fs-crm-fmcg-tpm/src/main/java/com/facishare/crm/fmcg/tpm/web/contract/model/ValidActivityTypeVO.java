package com.facishare.crm.fmcg.tpm.web.contract.model;

import com.alibaba.fastjson.annotation.JSONField;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.facishare.crm.fmcg.tpm.dao.mongo.po.ActivityNodeEntity;
import com.facishare.crm.fmcg.tpm.dao.mongo.po.ActivityTypePO;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.Data;
import lombok.ToString;
import org.apache.commons.collections4.CollectionUtils;

import java.io.Serializable;
import java.util.List;
import java.util.stream.Collectors;

/**
 * description : just code
 * <p>
 * create by @yangqf
 * create time 2021/11/17 16:24
 */
@Data
@ToString
@SuppressWarnings("Duplicates")
public class ValidActivityTypeVO implements Serializable {

    @JSONField(name = "_id")
    @JsonProperty(value = "_id")
    @SerializedName("_id")
    private String id;

    private String name;

    @JSONField(name = "activity_node_list")
    @JsonProperty(value = "activity_node_list")
    @SerializedName("activity_node_list")
    private List<ActivityNodeVO> activityNodeList = Lists.newArrayList();

    @JSONField(name = "current_object_record_type")
    @JsonProperty(value = "current_object_record_type")
    @SerializedName("current_object_record_type")
    private String currentObjectRecordType;

    @JSONField(name = "template_id")
    @JsonProperty(value = "template_id")
    @SerializedName("template_id")
    private String templateId;

    @JSONField(name = "is_disable_mobile")
    @JsonProperty(value = "is_disable_mobile")
    @SerializedName("is_disable_mobile")
    private Boolean isDisableMobile;

    public static ValidActivityTypeVO fromPO(ActivityTypePO po, String nodeObjectApiName) {
        if (po == null) {
            return null;
        }
        ValidActivityTypeVO vo = new ValidActivityTypeVO();
        vo.setId(po.getId().toString());
        vo.setName(po.getName());
        vo.setTemplateId(po.getTemplateId());
        if (!Strings.isNullOrEmpty(vo.getTemplateId()) && (vo.getTemplateId().startsWith("reward.") || vo.getTemplateId().startsWith("promotion."))) {
            vo.setIsDisableMobile(true);
        }
        vo.setActivityNodeList(po.getActivityNodes().stream().map(ActivityNodeVO::fromPOToValidActivityTypeVO).collect(Collectors.toList()));
        List<ActivityNodeEntity> nodes = po.getActivityNodes().stream().filter(node -> node.getObjectApiName().equals(nodeObjectApiName)).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(nodes)) {
            vo.setCurrentObjectRecordType(nodes.get(0).getObjectRecordType());
        }
        return vo;
    }
}
