package com.facishare.crm.fmcg.tpm.web.manager;

import com.facishare.crm.fmcg.common.apiname.ApiNames;
import com.facishare.crm.fmcg.tpm.dao.mongo.po.ActivityNodeEntity;
import com.facishare.crm.fmcg.tpm.dao.mongo.po.ActivityTypePO;
import com.facishare.crm.fmcg.tpm.dao.pg.ActivityProofMapper;
import com.facishare.crm.fmcg.tpm.dao.pg.po.ActivityTypeStatisticsDatumPO;
import com.facishare.crm.fmcg.tpm.web.contract.model.ActivityPlanReportDatumVO;
import com.facishare.crm.fmcg.tpm.web.contract.model.ActivityTypeReportDatumVO;
import com.facishare.crm.fmcg.tpm.web.manager.abstraction.IActivityProofManager;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * description : just code
 * <p>
 * create by @yangqf
 * create time 2021/12/22 15:08
 */
@Component
public class ActivityProofManager implements IActivityProofManager {

    @Resource
    private ActivityProofMapper activityProofMapper;

    @Override
    public ActivityTypeReportDatumVO loadActivityTypeReport(String tenantId, String activityTypeId, ActivityNodeEntity node, Map<String, String> displayNameMap, ActivityTypePO activityType) {

        Map<String, Long> activityPlanReportData = activityProofMapper.setTenantId(tenantId).queryActivityTypeStatisticsData(tenantId, activityTypeId)
                .stream()
                .collect(Collectors.toMap(ActivityTypeStatisticsDatumPO::getStatus, ActivityTypeStatisticsDatumPO::getCount));

        long total = activityPlanReportData.values().stream().mapToLong(v -> v).sum();
        return ActivityTypeReportDatumVO.builder()
                .templateId(node.getTemplateId())
                .type(node.getType())
                .nodeDisplayName(node.getName())
                .objectApiName(ApiNames.TPM_ACTIVITY_PROOF_OBJ)
                .objectDisplayName(displayNameMap.get(ApiNames.TPM_ACTIVITY_PROOF_OBJ))
                .count(total)
                .totalCount(total)
                .build();
    }

    @Override
    public ActivityPlanReportDatumVO loadActivityPlanReport(String tenantId, String activityPlanId) {
        return ActivityPlanReportDatumVO.builder()
                .count(activityProofMapper.setTenantId(tenantId).countByActivity(tenantId, activityPlanId))
                .build();
    }
}
