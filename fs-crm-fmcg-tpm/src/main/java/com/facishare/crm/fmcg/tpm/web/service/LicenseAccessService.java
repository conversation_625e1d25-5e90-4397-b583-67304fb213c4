package com.facishare.crm.fmcg.tpm.web.service;

import com.facishare.appserver.utils.EnvUtils;
import com.facishare.crm.fmcg.common.apiname.ApiNames;
import com.facishare.crm.fmcg.common.gray.TPMGrayUtils;
import com.facishare.crm.fmcg.common.http.ApiContext;
import com.facishare.crm.fmcg.common.http.ApiContextManager;
import com.facishare.crm.fmcg.tpm.business.TPM2Service;
import com.facishare.crm.fmcg.tpm.dao.mongo.ActivityTypeDAO;
import com.facishare.crm.fmcg.tpm.dao.mongo.po.ActivityTypePO;
import com.facishare.crm.fmcg.tpm.utils.I18NKeys;
import com.facishare.crm.fmcg.tpm.web.contract.LicenseAccessServiceGet;
import com.facishare.crm.fmcg.tpm.web.service.abstraction.BaseService;
import com.facishare.crm.fmcg.tpm.web.service.abstraction.ILicenseAccessService;
import com.facishare.paas.I18N;
import com.facishare.paas.auth.model.AuthContext;
import com.fxiaoke.paas.auth.factory.FuncClient;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Map;
import java.util.concurrent.atomic.AtomicReference;

@Slf4j
@Service
public class LicenseAccessService extends BaseService implements ILicenseAccessService {
    private static final String ROLE_APP_ID = "CRM";

    @Resource
    private FuncClient funcClient;
    @Resource
    private ActivityTypeDAO activityTypeDAO;
    @Resource
    private TPM2Service tpm2Service;

    private static final Map<String, Map<String, String>> ACCESS_ROLE_MAP = Maps.newHashMap();

    private static final Map<String, String> ACCESS_ROLE_DES_MAP = Maps.newHashMap();
    private static final Map<String, String> ACCESS_ROLE_LICENSE_CODE_MAP = Maps.newHashMap();

    static {
        Map<String, String> tpmPromotionFmcgAccessRole = Maps.newHashMap();
        tpmPromotionFmcgAccessRole.put(ApiNames.TPM_ACTIVITY_UNIFIED_CASE_OBJ, "tpm_promotion_unified");
        tpmPromotionFmcgAccessRole.put(ApiNames.TPM_ACTIVITY_OBJ, "tpm_promotion_plan");
        tpmPromotionFmcgAccessRole.put(ApiNames.TPM_ACTIVITY_AGREEMENT_OBJ, "tpm_promotion_agreement");
        tpmPromotionFmcgAccessRole.put(ApiNames.TPM_ACTIVITY_PROOF_OBJ, "tpm_promotion_proof");
        tpmPromotionFmcgAccessRole.put(ApiNames.TPM_ACTIVITY_PROOF_AUDIT_OBJ, "tpm_promotion_audit");
        tpmPromotionFmcgAccessRole.put(ApiNames.TPM_DEALER_ACTIVITY_COST, "tpm_promotion_dealer");
        tpmPromotionFmcgAccessRole.put(ApiNames.TPM_STORE_WRITE_OFF_OBJ, "tpm_promotion_cost");

        ACCESS_ROLE_MAP.put("tpmPromotionFmcgAccessRole", tpmPromotionFmcgAccessRole);
        Map<String, String> tpmBudgetFmcgAccessRole = Maps.newHashMap();
        tpmBudgetFmcgAccessRole.put(ApiNames.TPM_BUDGET_ACCOUNT, "tpm_budget_account");
        tpmBudgetFmcgAccessRole.put(ApiNames.TPM_BUDGET_DISASSEMBLY_OBJ, "tpm_budget_disassembly");
        tpmBudgetFmcgAccessRole.put(ApiNames.TPM_BUDGET_TRANSFER_DETAIL, "tpm_budget_transfer");
        tpmBudgetFmcgAccessRole.put(ApiNames.TPM_BUDGET_CARRY_FORWARD, "tpm_budget_carry");
        ACCESS_ROLE_MAP.put("tpmBudgetFmcgAccessRole", tpmBudgetFmcgAccessRole);

        ACCESS_ROLE_DES_MAP.put("tpmPromotionFmcgAccessRole", I18NKeys.FMCG_TPM_APP_TPM_ACCESS);
        ACCESS_ROLE_DES_MAP.put("tpmBudgetFmcgAccessRole", I18NKeys.FMCG_TPM_APP_BUDGET_ACCESS);

        ACCESS_ROLE_LICENSE_CODE_MAP.put("tpmPromotionFmcgAccessRole", "FMCG.TPM_LIMIT");
        ACCESS_ROLE_LICENSE_CODE_MAP.put("tpmBudgetFmcgAccessRole", "FMCG.BUDGET_LIMIT");
    }

    @Override
    public LicenseAccessServiceGet.Result get(LicenseAccessServiceGet.Arg arg) {
        ApiContext context = ApiContextManager.getContext();
        Integer employeeId = context.getEmployeeId();
        String outTenantId = context.getOutTenantId();
        String outUserId = context.getOutUserId();
        if (employeeId == null || StringUtil.isNotEmpty(outTenantId) || StringUtil.isNotEmpty(outUserId)) {
            log.info("license access check failed, employeeId:{}, outTenantId:{}, outUserId:{}", employeeId, outTenantId, outUserId);
            return success();
        }

        if (!gray(Integer.valueOf(context.getTenantId()))) {
            log.info("license access check failed, tenantId:{}", context.getTenantId());
            return success();
        }

        //码营销不需要限额许可
        String activityTypeId = arg.getActivityTypeId();
        if (StringUtil.isNotEmpty(activityTypeId)) {
            ActivityTypePO type = activityTypeDAO.get(context.getTenantId(), activityTypeId);
            String templateId = type.getTemplateId();
            if (StringUtil.isNotEmpty(templateId) && templateId.contains("reward")) {
                return success();
            }
        }

        String objApiName = arg.getObjApiName();
        //check
        AtomicReference<String> funcCodeAtomic = new AtomicReference<>("");
        AtomicReference<String> roleCode = new AtomicReference<>("");
        ACCESS_ROLE_MAP.forEach((role, apiNameMap) -> {
            if (apiNameMap.containsKey(objApiName)) {
                funcCodeAtomic.set(apiNameMap.get(objApiName));
                roleCode.set(role);

            }
        });

        String funcCode = funcCodeAtomic.get();
        if (StringUtil.isEmpty(funcCode)) {
            log.info(String.format("apiName:%s not found in access funcCode map", objApiName));
            return success();
        }

        if (ACCESS_ROLE_LICENSE_CODE_MAP.containsKey(roleCode.get())) {
            //没有购买许可的企业不需要校验许可
            if (!tpm2Service.existTPMLicenseCode(Integer.valueOf(context.getTenantId()), ACCESS_ROLE_LICENSE_CODE_MAP.get(roleCode.get()))) {
                return success();
            }
        }


        Boolean permissionCheck = userFuncPermissionCheck(context.getTenantId(), String.valueOf(employeeId), funcCode);
        if (permissionCheck) {
            return success();
        } else {
            return buildResult(false, "1001", String.format(I18N.text(I18NKeys.FMCG_TPM_APP_NO_ACCESS), I18N.text(ACCESS_ROLE_DES_MAP.get(roleCode.get()))));
        }
    }

    private boolean gray(Integer tenantId) {
        return !cloudGray() && TPMGrayUtils.isAllowAccessAuth(tenantId);
    }

    private boolean cloudGray() {
        return EnvUtils.checkExclusiveCloud();
    }

    private LicenseAccessServiceGet.Result success() {
        return buildResult(true, "", "");
    }

    private LicenseAccessServiceGet.Result buildResult(Boolean accessStatus, String errorCode, String errorMessage) {
        return LicenseAccessServiceGet.Result.builder().accessStatus(accessStatus).errorCode(errorCode).errorMessage(errorMessage).build();
    }


    public Boolean userFuncPermissionCheck(String tenantId, String userId, String funcCode) {
        Map<String, Boolean> funcPermissionCheck = funcClient.userFuncPermissionCheck(initAuthContext(tenantId, userId), Sets.newHashSet(funcCode));
        if (funcPermissionCheck.containsKey(funcCode)) {
            return funcPermissionCheck.get(funcCode);
        }
        return Boolean.FALSE;
    }

    AuthContext initAuthContext(String tenantId, String userId) {
        return AuthContext.builder().appId(ROLE_APP_ID).tenantId(tenantId).userId(userId).build();
    }
}
