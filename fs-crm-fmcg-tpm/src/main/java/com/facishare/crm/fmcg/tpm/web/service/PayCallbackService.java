package com.facishare.crm.fmcg.tpm.web.service;

import com.alibaba.fastjson.JSON;
import com.facishare.crm.fmcg.common.constant.PayConstants;
import com.facishare.crm.fmcg.tpm.utils.I18NKeys;
import com.facishare.paas.I18N;
import com.facishare.crm.fmcg.common.utils.IdentityIdGenerator;
import com.facishare.crm.fmcg.tpm.api.pay.PayCallbackDTO;
import com.facishare.crm.fmcg.tpm.reward.handler.BigDateHandler;
import com.facishare.crm.fmcg.tpm.web.service.abstraction.IPayCallbackService;
import com.facishare.open.common.utils.MsgEncryptor;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.github.trace.TraceContext;
import com.google.gson.Gson;
import com.google.gson.JsonObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.UUID;

/**
 * Author: linmj
 * Date: 2023/9/13 14:47
 */
@Slf4j
@Service
public class PayCallbackService implements IPayCallbackService {


    @Resource
    private BigDateHandler bigDateHandler;


    @Override
    public String dealPayCallback(String callbackContent) {
        String traceId = "callBack:" + UUID.randomUUID();
        log.info("deal pay callback, traceId: {}", traceId);
        TraceContext.get().setTraceId(traceId);
        Gson gson = new Gson();
        JsonObject jsonObject = gson.fromJson(callbackContent, JsonObject.class);
        String encryptStr = jsonObject.get("content").getAsString();
        try {
            JsonObject result = gson.fromJson(MsgEncryptor.decrypt(encryptStr, PayConstants.PAY_AES_CODE), JsonObject.class);
            log.info("Decrypt result: {}", result);
            PayCallbackDTO payCallbackDTO = gson.fromJson(result, PayCallbackDTO.class);
            if (payCallbackDTO.getMerchantOrderNo().startsWith(IdentityIdGenerator.STORE_PROMOTION_PREFIX)) {
                bigDateHandler.handle(payCallbackDTO.getMerchantOrderNo());
            } else {
                log.info("deal a default pay callback");
                throw new ValidateException(I18N.text(I18NKeys.PAY_CALLBACK_SERVICE_0));
                //todo:default
            }
            return "success";
        } catch (Exception e) {
            log.error("process content fail,", e);
            return "error";
        }
    }

    @Override
    public String dealPayCallbackNoEncrypt(String callbackContent) {
        try {
            log.info("Decrypt result: {}", callbackContent);
            PayCallbackDTO payCallbackDTO = JSON.parseObject(callbackContent, PayCallbackDTO.class);
            if (payCallbackDTO.getMerchantOrderNo().startsWith(IdentityIdGenerator.STORE_PROMOTION_PREFIX)) {
                bigDateHandler.handle(payCallbackDTO.getMerchantOrderNo());
            } else {
                log.info("deal a default pay callback");
                throw new ValidateException(I18N.text(I18NKeys.PAY_CALLBACK_SERVICE_0));
                //todo:default
            }
            return "success";
        } catch (Exception e) {
            log.error("process content fail,", e);
            return "error";
        }
    }

    @Override
    public String dealPayCallback2(String bizId) {

        try {
            if (bizId.startsWith(IdentityIdGenerator.STORE_PROMOTION_PREFIX)) {
                bigDateHandler.handle(bizId);
            } else {
                log.info("deal a default pay callback");
                throw new ValidateException(I18N.text(I18NKeys.PAY_CALLBACK_SERVICE_0));
            }
        }catch (Exception e){
            log.error("process dealPayCallback2 fail,", e);
            return "error";
        }
        return "success";
    }
}
