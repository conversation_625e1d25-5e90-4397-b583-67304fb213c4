package com.facishare.crm.fmcg.tpm.web.service;

import com.alibaba.fastjson.JSON;
import com.facishare.crm.fmcg.tpm.utils.I18NKeys;
import com.facishare.paas.I18N;
import com.alibaba.fastjson.JSONObject;
import com.facishare.common.parallel.ParallelUtils;
import com.facishare.crm.fmcg.common.apiname.*;
import com.facishare.crm.fmcg.common.gray.TPMGrayUtils;
import com.facishare.crm.fmcg.common.http.ApiContext;
import com.facishare.crm.fmcg.common.http.ApiContextManager;
import com.facishare.crm.fmcg.common.utils.QueryDataUtil;
import com.facishare.crm.fmcg.tpm.business.TPMTriggerActionService;
import com.facishare.crm.fmcg.tpm.business.enums.UseRangeEnum;
import com.facishare.crm.fmcg.tpm.dao.mongo.PromotionPolicyDAO;
import com.facishare.crm.fmcg.tpm.dao.mongo.po.ActiveStatusEnum;
import com.facishare.crm.fmcg.tpm.dao.mongo.po.ActivityTypeExt;
import com.facishare.crm.fmcg.tpm.dao.mongo.po.PromotionPolicyPO;
import com.facishare.crm.fmcg.tpm.service.abstraction.PluginInstanceService;
import com.facishare.crm.fmcg.tpm.utils.CommonUtils;
import com.facishare.crm.fmcg.tpm.utils.TimeUtils;
import com.facishare.crm.fmcg.tpm.utils.lock.RedisDistributedLock;
import com.facishare.crm.fmcg.tpm.web.contract.PromotionPolicyEdit;
import com.facishare.crm.fmcg.tpm.web.contract.PromotionPolicyGet;
import com.facishare.crm.fmcg.tpm.web.contract.StatisticPolicyOccupy;
import com.facishare.crm.fmcg.tpm.web.contract.TriggerAction;
import com.facishare.crm.fmcg.tpm.web.manager.abstraction.IActivityTypeManager;
import com.facishare.crm.fmcg.tpm.web.service.abstraction.BaseService;
import com.facishare.crm.fmcg.tpm.web.service.abstraction.IPromotionPolicyService;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.predef.action.BaseObjectSaveAction;
import com.facishare.paas.appframework.metadata.dto.UpdateMasterAndDetailData;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.impl.ObjectData;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.fmcg.framework.http.PaasDataProxy;
import com.fmcg.framework.http.contract.paas.data.GetConfigValueByKey;
import com.fxiaoke.crmrestapi.common.contants.LifeStatusEnum;
import com.github.jedis.support.MergeJedisCmd;
import com.github.trace.executor.MonitorTaskWrapper;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import de.lab4inf.math.util.Strings;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.BooleanUtils;
import org.apache.commons.lang.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/6/28 20:29
 */
//IgnoreI18nFile
@Slf4j
@Component
public class PromotionPolicyService extends BaseService implements IPromotionPolicyService {


    @Resource
    private PromotionPolicyDAO promotionPolicyDAO;

    @Resource
    private PaasDataProxy paasDataProxy;

    @Resource
    private TPMTriggerActionService triggerActionService;

    @Resource
    private IActivityTypeManager activityTypeManager;

    @Resource
    private PluginInstanceService pluginService;

    @Resource(name = "redisCmd")
    private MergeJedisCmd redisCmd;

    @Resource
    private RedisDistributedLock redisDistributedLock;

    @Override
    public void saveOrUpdate(String tenantId, String userId, IObjectData toObjectData,
                             String json, Boolean isUpdateLimitAccount) {
        if (Strings.isNullOrEmpty(json)) {
            return;
        }
        String objectId = toObjectData.getId();
        PromotionPolicyPO policyRulePO = new PromotionPolicyPO();
        policyRulePO.setObjectApiName(toObjectData.getDescribeApiName());
        policyRulePO.setObjectId(objectId);
        policyRulePO.setProductGiftData(json);

        PromotionPolicyPO promotionPolicy = getPromotionPolicyData(tenantId, toObjectData);
        if (Objects.isNull(promotionPolicy)) {
            promotionPolicyDAO.add(tenantId, Integer.parseInt(userId), policyRulePO);
        } else {
            if (!Objects.equals(promotionPolicy.getProductGiftData(), json)) {
                promotionPolicy.setProductGiftDataBackUp(promotionPolicy.getProductGiftData());
            } else {
                promotionPolicy.setProductGiftDataBackUp(json);
            }
            promotionPolicy.setProductGiftData(json);
            Map<String, Boolean> updateLimitAccount = new HashMap<>();
            if (ApiNames.TPM_ACTIVITY_OBJ.equals(toObjectData.getDescribeApiName())) {
                updateLimitAccount.put(objectId, isUpdateLimitAccount);
            }
            promotionPolicy.setUpdateLimitAccountMap(updateLimitAccount);
            promotionPolicyDAO.editPromotionPolicy(tenantId, Integer.parseInt(userId), promotionPolicy);
        }
    }

    @Override
    public PromotionPolicyGet.Result get(PromotionPolicyGet.Arg arg) {

        ApiContext context = ApiContextManager.getContext();
        PromotionPolicyPO promotionPolicyPO = promotionPolicyDAO.find(context.getTenantId(), arg.getObjectId(), arg.getObjectApiName());
        if (Objects.isNull(promotionPolicyPO)) {
            return PromotionPolicyGet.Result.builder().build();
        }

        return PromotionPolicyGet.Result.builder()
                .objectApiName(promotionPolicyPO.getObjectApiName())
                .objectId(promotionPolicyPO.getObjectId())
                .productGiftData(promotionPolicyPO.getProductGiftData())
                .build();
    }

    @Override
    public void revertPromotionPolicyRule(String tenantId, String userId, IObjectData toObjectData) {
        // 获取价格政策规则数据。
        PromotionPolicyPO promotionPolicyData = getPromotionPolicyData(tenantId, toObjectData);
        if (Objects.isNull(promotionPolicyData)) {
            return;
        }
        if (Strings.isNullOrEmpty(promotionPolicyData.getProductGiftDataBackUp())) {
            return;
        }

        promotionPolicyData.setProductGiftData(promotionPolicyData.getProductGiftDataBackUp());
        promotionPolicyDAO.editPromotionPolicy(tenantId, Integer.parseInt(userId), promotionPolicyData);
    }

    @Override
    public void createSFAPromotionPolicy(String tenantId, String userId, IObjectData toObjectData) {
        // 获取价格政策规则数据。
        PromotionPolicyPO promotionPolicyData = getPromotionPolicyData(tenantId, toObjectData);
        if (Objects.isNull(promotionPolicyData)) {
            return;
        }
        String pricePolicyId = createPricePolicy(tenantId, userId, toObjectData, promotionPolicyData, "create");
        log.info("createSFAPromotionPolicy pricePolicyLimitAccount pricePolicyId = {}, actionCode = {}", pricePolicyId, "create");
        String rioActivityId = toObjectData.get(TPMActivityFields.RIO_ACTIVITY_ID, String.class);
        if (TPMGrayUtils.isRioTenant(tenantId) && StringUtils.isNotEmpty(rioActivityId)) {
            asyncCreateRIOPricePolicyLimitAccount(tenantId, userId, toObjectData, pricePolicyId);
        } else {
            asyncCreatePricePolicyLimitAccount(tenantId, userId, toObjectData, pricePolicyId, "create");
        }

    }

    @Override
    public void asyncCreateRIOPricePolicyLimitAccount(String tenantId, String userId, IObjectData toObjectData, String pricePolicyId) {
        if (Strings.isNullOrEmpty(pricePolicyId)) {
            return;
        }
        log.info("RIO pricePolicyLimitAccount pricePolicyId = {}", pricePolicyId);
        String rioActivityId = toObjectData.get(TPMActivityFields.RIO_ACTIVITY_ID, String.class);
        if (Strings.isNullOrEmpty(rioActivityId)) {
            return;
        }

        String key = getUpdatePolicyLimitAccountKey(tenantId, rioActivityId);
        String uuid = UUID.randomUUID().toString();
        if (redisDistributedLock.tryLock(key, uuid)) {
            try {
                List<IObjectData> activities = queryActivityByRioActivityId(tenantId, rioActivityId);
                if (CollectionUtils.isEmpty(activities)) {
                    return;
                }
                BigDecimal activityAmountTotal = activities.stream()
                        .filter(activity -> {
                            BigDecimal amount = activity.get(TPMActivityFields.ACTIVITY_AMOUNT, BigDecimal.class);
                            return BigDecimal.ZERO.compareTo(amount) < 0;
                        })
                        .map(activity -> activity.get(TPMActivityFields.ACTIVITY_AMOUNT, BigDecimal.class))
                        .reduce(BigDecimal.ZERO, BigDecimal::add);


                IObjectData pricePolicyLimitAccount = findPricePolicyLimitAccount(tenantId, rioActivityId);

                TriggerAction.Arg pricePolicyLimitAccountArg;
                if (Objects.isNull(pricePolicyLimitAccount)) {
                    IObjectData addArg = buildRIOPricePolicyLimitAccountDataList(userId, pricePolicyId, rioActivityId, toObjectData, activityAmountTotal);
                    pricePolicyLimitAccountArg = getPricePolicyLimitAccoutActionArg(tenantId, "Add", addArg);
                } else {
                    pricePolicyLimitAccountArg = getPricePolicyLimitAccountArg(tenantId, pricePolicyId, activityAmountTotal, pricePolicyLimitAccount, "add");
                }
                log.info("RIO pricePolicyLimitAccount pricePolicyLimitAccountArg :{}", JSON.toJSONString(pricePolicyLimitAccountArg));
                BaseObjectSaveAction.Result result = triggerActionService.triggerAction(pricePolicyLimitAccountArg);
                log.info("RIO pricePolicyLimitAccount result :{}", JSON.toJSONString(result));
            } catch (Exception e) {
                log.error("RIO pricePolicyLimitAccount error, tenantId={}, pricePolicyId={},e", tenantId, pricePolicyId, e);
            } finally {
                redisDistributedLock.unlock(key, uuid);
            }
        }

    }

    private static String getUpdatePolicyLimitAccountKey(String tenantId, String rioActivityId) {
        return String.format("UPDATE_POLICY_LIMIT_ACCOUNT_DYNAMIC_LOCK_%s_%s", tenantId, rioActivityId);
    }


    private static TriggerAction.Arg getPricePolicyLimitAccountArg(String tenantId, String pricePolicyId, BigDecimal activityAmountTotal, IObjectData pricePolicyLimitAccount, String action) {
        pricePolicyLimitAccount.set(PricePolicyLimitAccountFields.LIMIT_NUMBER, activityAmountTotal);
        List<String> pricePolicyIds = pricePolicyLimitAccount.get(PricePolicyLimitAccountFields.PRICE_POLICY_IDS, List.class, Lists.newArrayList());
        if ("add".equals(action) && !pricePolicyIds.contains(pricePolicyId)) {
            pricePolicyIds.add(pricePolicyId);
        }
        if ("delete".equals(action)) {
            pricePolicyIds.remove(pricePolicyId);
        }

        pricePolicyLimitAccount.set(PricePolicyLimitAccountFields.PRICE_POLICY_IDS, pricePolicyIds);

        return getPricePolicyLimitAccoutActionArg(tenantId, "Edit", pricePolicyLimitAccount);
    }

    private static TriggerAction.Arg getPricePolicyLimitAccoutActionArg(String tenantId, String actionName, IObjectData pricePolicyLimitAccount) {
        return TriggerAction.Arg.builder()
                .actionName(actionName)
                .apiName(ApiNames.PRICE_POLICY_LIMIT_ACCOUNT_OBJ)
                .objectData(pricePolicyLimitAccount)
                .user(User.systemUser(tenantId))
                .triggerFlow(false)
                .triggerWorkflow(false)
                .build();
    }

    private IObjectData buildRIOPricePolicyLimitAccountDataList(String userId, String pricePolicyId, String rioActivityId, IObjectData activityData, BigDecimal activityAmountTotal) {
        IObjectData objectData = new ObjectData();

        objectData.setDescribeApiName(ApiNames.PRICE_POLICY_LIMIT_ACCOUNT_OBJ);
        objectData.setDataOwnDepartment(activityData.getDataOwnDepartment());
        objectData.setDataOwnOrganization(activityData.getDataOwnOrganization());
        objectData.setOutOwner(activityData.getOutOwner());
        objectData.setOwner(activityData.getOwner());
        objectData.setTenantId(activityData.getTenantId());
        objectData.setOutTenantId(activityData.getOutTenantId());
        objectData.setCreatedBy(userId);

        objectData.set(PricePolicyLimitAccountFields.PRICE_POLICY_IDS, Lists.newArrayList(pricePolicyId));
        objectData.set(PricePolicyLimitAccountFields.RANGE, PricePolicyLimitAccountFields.RANGE_MULTI_POLICY);
        objectData.set(PricePolicyLimitAccountFields.RIO_ACTIVITY_ID, rioActivityId);

        String modeType = activityData.get(TPMActivityFields.MODE_TYPE, String.class);
        String dimension = modeType.endsWith(PricePolicyLimitAccountFields.DIMENSION_GIFT) ? PricePolicyLimitAccountFields.DIMENSION_GIFT : PricePolicyLimitAccountFields.DIMENSION_SELF;
        objectData.set(PricePolicyLimitAccountFields.DIMENSION, dimension);

        if (PricePolicyLimitAccountFields.DIMENSION_GIFT.equals(dimension)) {
            objectData.set(PricePolicyLimitAccountFields.GIFT_TYPE, PricePolicyLimitAccountFields.GIFT_TYPE_ALL);
        }

        objectData.set(PricePolicyLimitAccountFields.LIMIT_OBJ_TYPE, PricePolicyLimitAccountFields.LIMIT_OBJ_TYPE_ALL);

        String limitType = dimension.equals(PricePolicyLimitAccountFields.DIMENSION_GIFT) ? PricePolicyLimitAccountFields.LIMIT_TYPE_GIFT_AMOUNT : PricePolicyLimitAccountFields.LIMIT_TYPE_ORIGINAL_AMOUNT;
        objectData.set(PricePolicyLimitAccountFields.LIMIT_TYPE, limitType);

        objectData.set(PricePolicyLimitAccountFields.LIMIT_NUMBER, activityAmountTotal);
        // 补充字段, 区分sfa和tpm 来源
        objectData.set(TPMTriggerActionService.REQUEST_FROM, TPMTriggerActionService.REQUEST_APP_NAME);
        return objectData;

    }

    private IObjectData findPricePolicyLimitAccount(String tenantId, String rioActivityId) {
        SearchTemplateQuery query = new SearchTemplateQuery();

        query.setLimit(-1);
        query.setOffset(0);
        query.setNeedReturnQuote(false);
        query.setNeedReturnCountNum(false);
        query.setSearchSource("db");

        Filter activityIdFilter = new Filter();
        activityIdFilter.setFieldName(PricePolicyLimitAccountFields.RIO_ACTIVITY_ID);
        activityIdFilter.setOperator(Operator.EQ);
        activityIdFilter.setFieldValues(Lists.newArrayList(rioActivityId));

        query.setFilters(Lists.newArrayList(activityIdFilter));
        List<IObjectData> limitAccountList = QueryDataUtil.find(serviceFacade, tenantId, ApiNames.PRICE_POLICY_LIMIT_ACCOUNT_OBJ, query, Lists.newArrayList());
        if (CollectionUtils.isEmpty(limitAccountList)) {
            return null;
        }
        return limitAccountList.get(0);
    }

    private List<IObjectData> queryActivityByRioActivityId(String tenantId, String rioActivityId) {
        SearchTemplateQuery query = new SearchTemplateQuery();

        query.setLimit(-1);
        query.setOffset(0);
        query.setNeedReturnQuote(false);
        query.setNeedReturnCountNum(false);
        query.setSearchSource("db");

        Filter activityIdFilter = new Filter();
        activityIdFilter.setFieldName(TPMActivityFields.RIO_ACTIVITY_ID);
        activityIdFilter.setOperator(Operator.EQ);
        activityIdFilter.setFieldValues(Lists.newArrayList(rioActivityId));

        query.setFilters(Lists.newArrayList(activityIdFilter));

        return QueryDataUtil.find(serviceFacade, tenantId, ApiNames.TPM_ACTIVITY_OBJ, query, Lists.newArrayList());
    }

    private String createPricePolicy(String tenantId,
                                     String userId,
                                     IObjectData toObjectData,
                                     PromotionPolicyPO promotionPolicyData,
                                     String actionCode) {
        Map<String, String> promotionPolicyIdMap = promotionPolicyData.getPromotionPolicyIdMap();
        // 创建价格政策 And 价格政策客户从对象
        TriggerAction.Arg arg = TriggerAction.Arg.builder()
                .actionName("Add")
                .apiName(ApiNames.PRICE_POLICY_OBJ)
                .objectData(buildPromotionPolicyData(toObjectData, promotionPolicyData.getProductGiftData(), userId, "", actionCode))
                .detailApiName(ApiNames.PRICE_POLICY_ACCOUNT_OBJ)
                .details(buildPricePolicyAccountData(toObjectData, tenantId, userId))
                .user(User.systemUser(tenantId))
                .triggerFlow(false)
                .triggerWorkflow(false)
                .build();

        BaseObjectSaveAction.Result result = triggerActionService.triggerAction(arg);
        if (Objects.isNull(result.getObjectData())) {
            return null;
        }
        // 存价格政策的id 到mongo中
        promotionPolicyIdMap.put(toObjectData.getId(), result.getObjectData().getId());
        promotionPolicyData.setPromotionPolicyIdMap(promotionPolicyIdMap);
        promotionPolicyDAO.editPromotionPolicy(tenantId, Integer.parseInt(userId), promotionPolicyData);
        return result.getObjectData().getId();
    }

    @Override
    public void updateSFAPromotionPolicy(String tenantId, String userId, IObjectData toObjectData) {
        // 获取价格政策规则数据。
        PromotionPolicyPO promotionPolicyData = getPromotionPolicyData(tenantId, toObjectData);
        if (Objects.isNull(promotionPolicyData)) {
            return;
        }
        String actionCode = "update";
        Map<String, String> promotionPolicyIdMap = promotionPolicyData.getPromotionPolicyIdMap();
        String pricePolicyId = promotionPolicyIdMap.get(toObjectData.getId());
        Map<String, Boolean> updateLimitAccountMap = promotionPolicyData.getUpdateLimitAccountMap();
        Boolean flag = updateLimitAccountMap.get(toObjectData.getId());
        if (Strings.isNullOrEmpty(pricePolicyId)) {
            // 没有找到政策，重新去生成。
            actionCode = "create";
            pricePolicyId = createPricePolicy(tenantId, userId, toObjectData, promotionPolicyData, actionCode);
            flag = Boolean.TRUE;
        } else {
            log.info("relation pricePolicy id is {}", promotionPolicyIdMap.get(toObjectData.getId()));
            updatePromotionPolicy(tenantId, userId, toObjectData, promotionPolicyData.getProductGiftData(), pricePolicyId, actionCode);
        }
        if (Boolean.TRUE.equals(flag)) {
            log.info("updateSFAPromotionPolicy pricePolicyLimitAccount pricePolicyId = {}, actionCode = {}", pricePolicyId, "update");
            String rioActivityId = toObjectData.get(TPMActivityFields.RIO_ACTIVITY_ID, String.class);
            if (TPMGrayUtils.isRioTenant(tenantId) && StringUtils.isNotEmpty(rioActivityId)) {
                asyncCreateRIOPricePolicyLimitAccount(tenantId, userId, toObjectData, pricePolicyId);
            } else {
                asyncCreatePricePolicyLimitAccount(tenantId, userId, toObjectData, pricePolicyId, actionCode);
            }
        }
    }

    private void updateActivityModeType(String tenantId, List<IObjectData> toObjectData, String modeType) {
        if (!Strings.isNullOrEmpty(modeType)) {
            Map<String, Object> updateMap = new HashMap<>();
            updateMap.put(TPMActivityFields.MODE_TYPE, modeType);
            serviceFacade.batchUpdateWithMap(User.systemUser(tenantId), toObjectData, updateMap);
        }
    }

    private void updatePromotionPolicy(String tenantId,
                                       String userId,
                                       IObjectData toObjectData,
                                       String productGiftData,
                                       String pricePolicyId,
                                       String actionCode) {

        // 更新价格政策 And 价格政策客户从对象
        TriggerAction.Arg arg = TriggerAction.Arg.builder()
                .actionName("Edit")
                .apiName(ApiNames.PRICE_POLICY_OBJ)
                .objectData(buildPromotionPolicyData(toObjectData, productGiftData, userId, pricePolicyId, actionCode))
                .detailApiName(ApiNames.PRICE_POLICY_ACCOUNT_OBJ)
                .details(buildPricePolicyAccountData(toObjectData, tenantId, userId))
                .user(User.systemUser(tenantId))
                .triggerFlow(false)
                .triggerWorkflow(false)
                .build();

        BaseObjectSaveAction.Result result = triggerActionService.triggerAction(arg);
    }

    private void asyncCreatePricePolicyLimitAccount(String tenantId,
                                                    String userId,
                                                    IObjectData toObjectData,
                                                    String pricePolicyId,
                                                    String actionCode) {
        if (Strings.isNullOrEmpty(pricePolicyId)) {
            return;
        }
        log.info("pricePolicyLimitAccount actionCode ={} , pricePolicyId = {}", actionCode, pricePolicyId);
        ParallelUtils.createBackgroundTask().submit(MonitorTaskWrapper.wrap(() -> {
            log.info("pricePolicyLimitAccount init task actionCode ={} , pricePolicyId = {}", actionCode, pricePolicyId);
            //作废删除
            if ("update".equals(actionCode)) {
                validAndDeletePricePolicyLimitAccount(tenantId, userId, toObjectData.getId(), pricePolicyId);
            }
            //再新建
            List<IObjectData> dataListArg = buildPricePolicyLimitAccountDataList(toObjectData, pricePolicyId, userId);
            if (CollectionUtils.isNotEmpty(dataListArg)) {
                for (IObjectData addArg : dataListArg) {
                    log.info(" pricePolicyLimitAccount arg :{}", JSON.toJSONString(addArg));
                    TriggerAction.Arg pricePolicyLimitAccountArg = TriggerAction.Arg.builder()
                            .actionName("Add")
                            .apiName(ApiNames.PRICE_POLICY_LIMIT_ACCOUNT_OBJ)
                            .objectData(addArg)
                            .user(User.systemUser(tenantId))
                            .triggerFlow(false)
                            .triggerWorkflow(false)
                            .build();

                    BaseObjectSaveAction.Result result = triggerActionService.triggerAction(pricePolicyLimitAccountArg);
                    log.info(" pricePolicyLimitAccount result :{}", JSON.toJSONString(result));
                }
            }
        })).run();
    }

    @Override
    public void updateSFAPromotionPolicyTest(String id) {
        ApiContext context = ApiContextManager.getContext();
        IObjectData activity = serviceFacade.findObjectData(User.systemUser(context.getTenantId()), id, ApiNames.TPM_ACTIVITY_OBJ);

        updateSFAPromotionPolicy(context.getTenantId(), context.getEmployeeId().toString(), activity);
    }

    @Override
    public void createSFAPromotionPolicyTest(String id) {
        ApiContext context = ApiContextManager.getContext();
        IObjectData activity = serviceFacade.findObjectData(User.systemUser(context.getTenantId()), id, ApiNames.TPM_ACTIVITY_OBJ);

        createSFAPromotionPolicy(context.getTenantId(), context.getEmployeeId().toString(), activity);
    }

    @Override
    public StatisticPolicyOccupy.Result statisticPolicyOccupy(StatisticPolicyOccupy.Arg arg) {
        List<StatisticPolicyOccupy.DetailLimitObj> detailLimitObjs = arg.getDataList();
        String tenantId = arg.getTenantId();
        if (CollectionUtils.isEmpty(detailLimitObjs)) {
            return StatisticPolicyOccupy.Result.builder().code("0").msg("SUCCESS").build();
        }

        //总的明细限量
        Map<String, List<StatisticPolicyOccupy.DetailLimitObj>> activityDetailLimitObj = detailLimitObjs.stream().collect(Collectors.groupingBy(StatisticPolicyOccupy.DetailLimitObj::getActivityId));
        activityDetailLimitObj.forEach((activityId, details) -> {
            //修改主对象活动申请锁
            String key = String.format("STATISTIC_POLICY_DYNAMIC_LOCK_%s_%s", tenantId, activityId);
            String uuid = UUID.randomUUID().toString();
            if (redisDistributedLock.tryLock(key, uuid)) {
                try {
                    if (validationIsMultiPolicy(details)) {
                        updateActivityByMultiPolicyOccupy(tenantId, activityId, details);
                    } else {
                        updateActivityMasterAndDetails(tenantId, activityId, details);
                    }
                } catch (Exception e) {
                    log.error("statisticPolicyOccupy error : ", e);
                } finally {
                    redisDistributedLock.unlock(key, uuid);
                }
            }
        });
        return StatisticPolicyOccupy.Result.builder().code("0").msg("SUCCESS").build();
    }

    private void updateActivityMasterAndDetails(String tenantId, String activityId, List<StatisticPolicyOccupy.DetailLimitObj> details) {
        List<IObjectData> updateDetails = getNeedUpdateDetails(tenantId, activityId, details);

        List<StatisticPolicyOccupy.DetailLimitObj> allLimitTypeDetailLimits = details.stream()
                .filter(detailLimitObj -> "all".equals(detailLimitObj.getLimitObjType()))
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(allLimitTypeDetailLimits)) {
            throw new ValidateException(I18N.text(I18NKeys.PROMOTION_POLICY_SERVICE_0));
        }

        if (allLimitTypeDetailLimits.size() > 1) {
            throw new ValidateException(I18N.text(I18NKeys.PROMOTION_POLICY_SERVICE_1));
        }

        IObjectData activityObj = serviceFacade.findObjectDataByIds(tenantId, Lists.newArrayList(activityId), ApiNames.TPM_ACTIVITY_OBJ).get(0);
        activityObj.set(TPMActivityFields.TOTAL_POLICY_DYNAMIC_AMOUNT, allLimitTypeDetailLimits.get(0).getOccupy());

        //在锁中更新，因为无法判断具体有多少个活动进来，锁不住所有数据
        Map<String, List<IObjectData>> detailsToUpdate = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(updateDetails)) {
            detailsToUpdate.put(ApiNames.TPM_ACTIVITY_STORE_OBJ, updateDetails);
        }
        UpdateMasterAndDetailData.Arg updateMasterAndDetailArg = UpdateMasterAndDetailData.Arg.builder().masterObjectData(activityObj).detailsToUpdate(detailsToUpdate).build();
        serviceFacade.updateMasterAndDetailData(User.systemUser(tenantId), updateMasterAndDetailArg);
    }

    private void updateActivityByMultiPolicyOccupy(String tenantId, String activityId, List<StatisticPolicyOccupy.DetailLimitObj> details) {
        //过滤RIO特有的 一个活动既是多价格政策又是搭赠，搭赠不参与回填占用金额
        details = details.stream().filter(detailLimitObj -> PricePolicyLimitAccountFields.RANGE_MULTI_POLICY.equals(detailLimitObj.getRange())).collect(Collectors.toList());
        BigDecimal sumPolicyOccupy = BigDecimal.ZERO;
        for (StatisticPolicyOccupy.DetailLimitObj policy : details) {
            BigDecimal occupy = policy.getOccupy();
            if (Objects.nonNull(occupy)) {
                sumPolicyOccupy = sumPolicyOccupy.add(occupy);
            }
        }

        IObjectData activityObj = serviceFacade.findObjectDataByIds(tenantId, Lists.newArrayList(activityId), ApiNames.TPM_ACTIVITY_OBJ).get(0);
        activityObj.set(TPMActivityFields.TOTAL_POLICY_DYNAMIC_AMOUNT, sumPolicyOccupy);

        Map<String, Object> updateMap = new HashMap<>();
        updateMap.put(TPMActivityFields.TOTAL_POLICY_DYNAMIC_AMOUNT, sumPolicyOccupy);
        serviceFacade.updateWithMap(User.systemUser(tenantId), activityObj, updateMap);
    }

    private boolean validationIsMultiPolicy(List<StatisticPolicyOccupy.DetailLimitObj> details) {
        return details.stream().anyMatch(detailLimitObj -> PricePolicyLimitAccountFields.RANGE_MULTI_POLICY.equals(detailLimitObj.getRange()));
    }

    private List<IObjectData> getNeedUpdateDetails(String tenantId, String activityId, List<StatisticPolicyOccupy.DetailLimitObj> details) {

        List<IObjectData> tpmActivityStoreObjs = Lists.newArrayList();
        //客户限制类型为差额的明细限量
        List<StatisticPolicyOccupy.DetailLimitObj> differenceAccountTypeDetailLimits = details.stream()
                .filter(detailLimitObj -> "DIFFERENCE".equals(detailLimitObj.getAccountMode()) && !org.elasticsearch.common.Strings.isEmpty(detailLimitObj.getAccountId()))
                .collect(Collectors.toList());


        if (!CollectionUtils.isEmpty(differenceAccountTypeDetailLimits)) {
            Map<String, BigDecimal> accountAmountMap = differenceAccountTypeDetailLimits
                    .stream().collect(Collectors.toMap(StatisticPolicyOccupy.DetailLimitObj::getAccountId, StatisticPolicyOccupy.DetailLimitObj::getOccupy));

            List<String> accountIds = differenceAccountTypeDetailLimits.stream().map(StatisticPolicyOccupy.DetailLimitObj::getAccountId).collect(Collectors.toList());
            tpmActivityStoreObjs = queryActivityStore(tenantId, activityId, accountIds);

            for (IObjectData store : tpmActivityStoreObjs) {
                String storeId = store.get(TPMActivityStoreFields.STORE_ID, String.class);
                store.set(TPMActivityStoreFields.USED_LIMIT_AMOUNT, accountAmountMap.get(storeId));
            }
        }

        return tpmActivityStoreObjs;
    }

    private List<IObjectData> queryActivityStore(String tenantId, String activityId, List<String> storeIds) {
        SearchTemplateQuery query = new SearchTemplateQuery();

        query.setLimit(-1);
        query.setOffset(0);
        query.setNeedReturnQuote(false);
        query.setNeedReturnCountNum(false);
        query.setSearchSource("db");

        Filter activityIdFilter = new Filter();
        activityIdFilter.setFieldName(TPMActivityStoreFields.ACTIVITY_ID);
        activityIdFilter.setOperator(Operator.EQ);
        activityIdFilter.setFieldValues(Lists.newArrayList(activityId));

        Filter accountIdFilter = new Filter();
        accountIdFilter.setFieldName(TPMActivityStoreFields.STORE_ID);
        accountIdFilter.setOperator(Operator.IN);
        accountIdFilter.setFieldValues(Lists.newArrayList(storeIds));

        query.setFilters(Lists.newArrayList(activityIdFilter, accountIdFilter));

        return QueryDataUtil.find(serviceFacade, tenantId, ApiNames.TPM_ACTIVITY_STORE_OBJ, query, Lists.newArrayList());
    }

    private void validAndDeletePricePolicyLimitAccount(String tenantId, String userId, String activityId, String pricePolicyId) {
        List<IObjectData> pricePolicyLimitAccountList = queryPricePolicyRelatedData(tenantId, activityId, pricePolicyId);
        if (CollectionUtils.isEmpty(pricePolicyLimitAccountList)) {
            return;
        }
        serviceFacade.bulkInvalid(pricePolicyLimitAccountList, User.builder().userId(userId).tenantId(tenantId).build());
    }

    private void handlePricePolicy(String tenantId, String userId, String pricePolicyId, String name, String id, String activeStatus) {
        try {
            //禁用价格政策
            IObjectData pricePolicyData = queryPricePolicyData(tenantId, pricePolicyId);
            if (Objects.nonNull(pricePolicyData)) {
                if (activeStatus.equals(pricePolicyData.get("active_status", String.class, ""))) {
                    return;
                }
                Map<String, Object> map = new HashMap<>();
                map.put("active_status", activeStatus);
                map.put("name", name + "-" + id);
                serviceFacade.updateWithMap(User.builder().userId(userId).tenantId(tenantId).build(), pricePolicyData, map);
            }
        } catch (Exception exception) {
            log.info("handlerPricePolicy fail message is {}", exception.getMessage());
        }
    }

    private IObjectData queryPricePolicyData(String tenantId, String pricePolicyId) {
        if (Strings.isNullOrEmpty(pricePolicyId)) {
            return null;
        }
        return serviceFacade.findObjectData(User.systemUser(tenantId), pricePolicyId, ApiNames.PRICE_POLICY_OBJ);
    }

    private List<IObjectData> queryPricePolicyRelatedData(String tenantId,
                                                          String activityId,
                                                          String pricePolicyId) {
        if (Strings.isNullOrEmpty(pricePolicyId)) {
            return new ArrayList<>();
        }

        SearchTemplateQuery stq = new SearchTemplateQuery();

        stq.setLimit(2000);
        stq.setOffset(0);
        stq.setNeedReturnCountNum(false);
        stq.setNeedReturnQuote(false);
        stq.setSearchSource("db");

        Filter pricePolicyFilter = new Filter();
        pricePolicyFilter.setFieldName(PricePolicyLimitAccountFields.PRICE_POLICY_ID);
        pricePolicyFilter.setOperator(Operator.EQ);
        pricePolicyFilter.setFieldValues(Lists.newArrayList(pricePolicyId));

        stq.setFilters(Lists.newArrayList(pricePolicyFilter));

        if (!Strings.isNullOrEmpty(activityId)) {
            Filter activityFilter = new Filter();
            activityFilter.setFieldName(PricePolicyLimitAccountFields.ACTIVITY_ID);
            activityFilter.setOperator(Operator.EQ);
            activityFilter.setFieldValues(Lists.newArrayList(activityId));
            stq.getFilters().add(activityFilter);
        }

        return serviceFacade.findBySearchQueryIgnoreAll(
                User.systemUser(tenantId),
                ApiNames.PRICE_POLICY_LIMIT_ACCOUNT_OBJ,
                stq
        ).getData();
    }

    @Override
    public void batchUpdateSFAPromotionPolicy(String tenantId, String userId, String modeType, List<IObjectData> objectDataList) {
        if (CollectionUtils.isEmpty(objectDataList)) {
            return;
        }

        if (StringUtils.isNotEmpty(modeType)) {
            List<IObjectData> needUpdateModeTypes = objectDataList.stream()
                    .filter(o -> !modeType.equals(o.get(TPMActivityFields.MODE_TYPE, String.class, ""))).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(needUpdateModeTypes)) {
                //批量更新活动申请
                List<List<IObjectData>> partition = Lists.partition(needUpdateModeTypes, 200);
                for (List<IObjectData> toObjectDataList : partition) {
                    updateActivityModeType(tenantId, toObjectDataList, modeType);
                }
            }
        }

        log.info("update relation activity list size is {}", objectDataList.size());
        // flag: false 方案更新，不带 价格政策明细限量
        ParallelUtils.createBackgroundTask().submit(MonitorTaskWrapper.wrap(() ->
                objectDataList.forEach(objectData -> {
                            PromotionPolicyPO promotionPolicyData = getPromotionPolicyData(tenantId, objectData);
                            if (Objects.isNull(promotionPolicyData)) {
                                return;
                            }
                            Map<String, String> activityPromotionPolicyMap = promotionPolicyData.getPromotionPolicyIdMap();
                            String promotionPolicyId = activityPromotionPolicyMap.get(objectData.getId());
                            if (Strings.isNullOrEmpty(promotionPolicyId)) {
                                return;
                            }
                            updatePromotionPolicy(tenantId, userId, objectData, promotionPolicyData.getProductGiftData(), promotionPolicyId, "update");
                        }
                ))).run();
    }

    @Override
    public void batchInvalidPromotionPolicy(String tenantId, String userId, List<IObjectData> objectDataList) {
        if (CollectionUtils.isEmpty(objectDataList)) {
            return;
        }
        log.info("invalid relation activity list size is {}", objectDataList.size());
        objectDataList.forEach(objectData -> disablePromotionPolicy(tenantId, userId, objectData));
    }

    @Override
    public void disablePromotionPolicy(String tenantId, String userId, IObjectData toObjectData) {
        PromotionPolicyPO promotionPolicyData = getPromotionPolicyData(tenantId, toObjectData);
        if (Objects.isNull(promotionPolicyData)) {
            return;
        }
        Map<String, String> promotionPolicyIdMap = promotionPolicyData.getPromotionPolicyIdMap();
        String pricePolicyId = promotionPolicyIdMap.get(toObjectData.getId());
        String name = toObjectData.getName();
        log.info("disable PromotionPolicy id is {}, userId is {}", pricePolicyId, userId);
        // 禁用价格政策 相关。
        handlePricePolicy(tenantId, userId, pricePolicyId, name, toObjectData.getId(), ActiveStatusEnum.DISABLE.code());

        if (TPMGrayUtils.isRioTenant(tenantId)) {
            String rioActivityId = toObjectData.get(TPMActivityFields.RIO_ACTIVITY_ID, String.class);
            if (Strings.isNullOrEmpty(rioActivityId)) {
                return;
            }

            String key = getUpdatePolicyLimitAccountKey(tenantId, rioActivityId);
            String uuid = UUID.randomUUID().toString();
            if (redisDistributedLock.tryLock(key, uuid)) {
                try {
                    IObjectData pricePolicyLimitAccount = findPricePolicyLimitAccount(tenantId, rioActivityId);
                    if (Objects.isNull(pricePolicyLimitAccount)) {
                        return;
                    }
                    BigDecimal limitNumber = pricePolicyLimitAccount.get(PricePolicyLimitAccountFields.LIMIT_NUMBER, BigDecimal.class);
                    BigDecimal activityAmount = toObjectData.get(TPMActivityFields.ACTIVITY_AMOUNT, BigDecimal.class);
                    BigDecimal activityAmountTotal = limitNumber.subtract(activityAmount);
                    List<String> pricePolicyIds = pricePolicyLimitAccount.get(PricePolicyLimitAccountFields.PRICE_POLICY_IDS, List.class);
                    if (CollectionUtils.isEmpty(pricePolicyIds)) {
                        return;
                    }
                    int size = pricePolicyIds.size();
                    //编辑
                    if (size > 1 && pricePolicyIds.contains(pricePolicyId)) {
                        TriggerAction.Arg pricePolicyLimitAccountArg = getPricePolicyLimitAccountArg(tenantId, pricePolicyId, activityAmountTotal, pricePolicyLimitAccount, "delete");
                        log.info("RIO pricePolicyLimitAccount pricePolicyLimitAccountArg :{}", JSON.toJSONString(pricePolicyLimitAccountArg));
                        BaseObjectSaveAction.Result result = triggerActionService.triggerAction(pricePolicyLimitAccountArg);
                    }
                    //作废
                    if (size == 1 && pricePolicyIds.contains(pricePolicyId)) {
                        serviceFacade.invalid(pricePolicyLimitAccount, User.builder().userId(userId).tenantId(tenantId).build());
                    }
                } finally {
                    redisDistributedLock.unlock(key, uuid);
                }
            }
        }
    }

    @Override
    public void enablePromotionPolicy(String tenantId, String userId, IObjectData toObjectData) {
        PromotionPolicyPO promotionPolicyData = getPromotionPolicyData(tenantId, toObjectData);
        if (Objects.isNull(promotionPolicyData)) {
            return;
        }
        Map<String, String> promotionPolicyIdMap = promotionPolicyData.getPromotionPolicyIdMap();
        String pricePolicyId = promotionPolicyIdMap.get(toObjectData.getId());
        String name = toObjectData.getName();
        log.info("enable PromotionPolicy id is {}, userId is {}", pricePolicyId, userId);
        // 启用价格政策
        handlePricePolicy(tenantId, userId, pricePolicyId, name, toObjectData.getId(), ActiveStatusEnum.ENABLE.code());

        if (TPMGrayUtils.isRioTenant(tenantId)) {
            String rioActivityId = toObjectData.get(TPMActivityFields.RIO_ACTIVITY_ID, String.class);
            if (Strings.isNullOrEmpty(rioActivityId)) {
                return;
            }
            log.info("RIO enablePromotionPolicy tenantId:{}, userId:{},rioActivityId:{}", tenantId, userId, rioActivityId);
            asyncCreateRIOPricePolicyLimitAccount(tenantId, userId, toObjectData, pricePolicyId);
        }
    }

    private IObjectData buildPromotionPolicyData(IObjectData toObjectData,
                                                 String productGiftData,
                                                 String userId,
                                                 String pricePolicyId,
                                                 String actionCode) {
        Long beginDate = toObjectData.get(TPMActivityFields.BEGIN_DATE, Long.class);
        Long endDate = toObjectData.get(TPMActivityFields.END_DATE, Long.class);
        String name = toObjectData.get(TPMActivityFields.NAME, String.class);
        String modeTypeByData = toObjectData.get(TPMActivityFields.MODE_TYPE, String.class, "");
        List<String> owner = toObjectData.getOwner();
        JSONObject productGiftJson = JSONObject.parseObject(productGiftData);
        String modeType = (String) CommonUtils.getOrDefault(productGiftJson.get(TPMActivityFields.MODE_TYPE), "");
        modeType = Strings.isNullOrEmpty(modeType) ? modeTypeByData : modeType;
        // 申请的门店范围，
        String storeRange = toObjectData.get(TPMActivityFields.STORE_RANGE, String.class);
        IObjectData objectData = new ObjectData();
        objectData.setOwner(owner);
        // 政策id
        objectData.setId(pricePolicyId);
        objectData.set(TPMActivityFields.SOURCE_OBJECT_API_NAME, "SalesOrderObj");
        objectData.setDescribeApiName(ApiNames.PRICE_POLICY_OBJ);
        objectData.setCreatedBy(userId);

        objectData.set(PricePolicyFields.ACTIVITY_ID, toObjectData.getId());
        objectData.set(PricePolicyFields.START_DATE, beginDate <= TimeUtils.MIN_DATE ? null : beginDate);
        objectData.set(PricePolicyFields.END_DATE, endDate >= TimeUtils.MAX_DATE ? null : endDate);
        objectData.set(PricePolicyFields.NAME, name);
        objectData.set(PricePolicyFields.PRODUCT_GIFT_DATA_JSON, productGiftJson);
        objectData.set(PricePolicyFields.ACCOUNT_RANGE, buildAccountRange(storeRange));
        objectData.set(PricePolicyFields.MODE_TYPE, modeType);
        objectData.set(PricePolicyFields.PRIORITY, "2");
        if (modeType.startsWith("master")) {
            objectData.set(PricePolicyFields.MODIFY_TYPE, PricePolicyFields.MODIFY_TYPE__MASTER);
        } else if (modeType.startsWith("details")) {
            objectData.set(PricePolicyFields.MODIFY_TYPE, PricePolicyFields.MODIFY_TYPE__DETAIL);
        }
        // 计算状态
        objectData.set(PricePolicyFields.CALCULATE_STATUS, "0");
        // actionCode
        if ("create".equals(actionCode)) {
            objectData.set(PricePolicyFields.ACTIVE_STATUS, PricePolicyFields.ACTIVE_STATUS__ENABLE);
        }
        // 补充字段, 区分sfa和tpm 来源
        objectData.set(TPMTriggerActionService.REQUEST_FROM, TPMTriggerActionService.REQUEST_APP_NAME);
        return objectData;
    }

    private List<IObjectData> buildPricePolicyAccountData(IObjectData activityData, String tenantId, String userId) {
        String storeRange = activityData.get(TPMActivityFields.STORE_RANGE, String.class);
        // 申请的门店范围
        JSONObject rangeObj = JSON.parseObject(storeRange);
        if (rangeObj.get("type") == null) {
            return Lists.newArrayList();
        }

        List<IObjectData> pricePolicyAccounts = Lists.newArrayList();
        if (rangeObj.getString("type").toUpperCase().equals(UseRangeEnum.FIXED.value())) {
            List<IObjectData> detailList = queryActivityStoreByActivityId(tenantId, activityData.getId());
            for (IObjectData detail : detailList) {
                IObjectData objectData = getPricePolicyAccountData(activityData, userId, detail.get(TPMActivityStoreFields.STORE_ID, String.class));
                pricePolicyAccounts.add(objectData);
            }
        }
        return pricePolicyAccounts;
    }


    @Override
    public void bindPluginInstance(String tenantId, int employeeId, String apiName, String pluginName) {
        //查询对象是否已绑定插件
        if (pluginService.findPluginUnit(tenantId, apiName, pluginName)) {
            return;
        }
        try {
            pluginService.addPluginUnit(Integer.valueOf(tenantId), employeeId, apiName, pluginName);
        } catch (Exception e) {
            log.error("add plugin instance fail, e:", e);
        }
    }

    @NotNull
    private IObjectData getPricePolicyAccountData(IObjectData toObjectData, String userId, String accountId) {
        IObjectData objectData = new ObjectData();

        objectData.setDescribeApiName(ApiNames.PRICE_POLICY_ACCOUNT_OBJ);
        objectData.setDataOwnDepartment(toObjectData.getDataOwnDepartment());
        objectData.setDataOwnOrganization(toObjectData.getDataOwnOrganization());
        objectData.setOutOwner(toObjectData.getOutOwner());
        objectData.setOwner(toObjectData.getOwner());
        objectData.setTenantId(toObjectData.getTenantId());
        objectData.setOutTenantId(toObjectData.getOutTenantId());
        objectData.setCreatedBy(userId);

        objectData.set(PricePolicyAccountFields.ACCOUNT_ID, accountId);
        objectData.set(PricePolicyAccountFields.APPLY_RANGE, UseRangeEnum.FIXED.value());
        return objectData;
    }

    private List<IObjectData> queryActivityStoreByActivityId(String tenantId, String activityId) {
        SearchTemplateQuery stq = new SearchTemplateQuery();

        stq.setLimit(2000);
        stq.setOffset(0);
        stq.setNeedReturnCountNum(false);
        stq.setNeedReturnQuote(false);
        stq.setSearchSource("db");

        Filter activityFilter = new Filter();
        activityFilter.setFieldName(TPMActivityStoreFields.ACTIVITY_ID);
        activityFilter.setOperator(Operator.EQ);
        activityFilter.setFieldValues(Lists.newArrayList(activityId));

        stq.setFilters(Lists.newArrayList(activityFilter));

        List<IObjectData> detailDataList = serviceFacade.findBySearchQueryWithFieldsIgnoreAll(
                User.systemUser(tenantId),
                ApiNames.TPM_ACTIVITY_STORE_OBJ,
                stq,
                Lists.newArrayList(CommonFields.ID, TPMActivityStoreFields.POLICY_LIMIT_AMOUNT, TPMActivityStoreFields.STORE_ID)).getData();

        if (CollectionUtils.isEmpty(detailDataList)) {
            return Lists.newArrayList();
        }
        return detailDataList;
    }

    private String buildAccountRange(String storeRange) {
        JSONObject rangeObj = JSON.parseObject(storeRange);
        if (rangeObj.get("type") != null &&
                rangeObj.getString("type").toUpperCase().equals(UseRangeEnum.CONDITION.value())) {
            String code = rangeObj.getString("code");
            if (!Strings.isNullOrEmpty(code)) {
                rangeObj.remove(code);
            }
        }
        return rangeObj.toJSONString();
    }


    private List<IObjectData> buildPricePolicyLimitAccountDataList(IObjectData activityData, String
            pricePolicyId, String userId) {

        boolean isMainData = true;
        List<IObjectData> dataList = Lists.newArrayList();
        IObjectData mainObjectData = getCreatePricePolicyLimitAccountData(pricePolicyId, userId, "", isMainData, activityData, BigDecimal.ZERO);
        dataList.add(mainObjectData);


        String storeRange = activityData.get(TPMActivityFields.STORE_RANGE, String.class);
        // 申请的门店范围
        JSONObject rangeObj = JSON.parseObject(storeRange);

        String activityLimitObjType = activityData.get(TPMActivityFields.LIMIT_OBJ_TYPE, String.class);

        //等额
        if (TPMActivityFields.LIMIT_OBJ_TYPE_ACCOUNT_COST_EQUAL.equals(activityLimitObjType)) {
            isMainData = false;
            BigDecimal accountUnityLimitAmount = activityData.get(TPMActivityFields.ACCOUNT_UNIFY_LIMIT_AMOUNT, BigDecimal.class, BigDecimal.ZERO);
            IObjectData accountObjectData = getCreatePricePolicyLimitAccountData(pricePolicyId, userId, "", isMainData, activityData, accountUnityLimitAmount);
            dataList.add(accountObjectData);
        }

        //差额
        if (TPMActivityFields.LIMIT_OBJ_TYPE_ACCOUNT_COST_DIFFERENCE.equals(activityLimitObjType)) {
            if (rangeObj.getString("type").toUpperCase().equals(UseRangeEnum.FIXED.value())) {

                List<IObjectData> detailList = queryActivityStoreByActivityId(activityData.getTenantId(), activityData.getId());
                if (CollectionUtils.isNotEmpty(detailList)) {
                    isMainData = false;
                    for (IObjectData activityStoreData : detailList) {
                        String accountId = activityStoreData.get(TPMActivityStoreFields.STORE_ID, String.class, "");
                        BigDecimal storePolicyLimitAmount = activityStoreData.get(TPMActivityStoreFields.POLICY_LIMIT_AMOUNT, BigDecimal.class, BigDecimal.ZERO);
                        IObjectData objectData = getCreatePricePolicyLimitAccountData(pricePolicyId, userId, accountId, isMainData, activityData, storePolicyLimitAmount);
                        dataList.add(objectData);
                    }
                }
            }
        }

        return dataList;
    }

    @NotNull
    private IObjectData getCreatePricePolicyLimitAccountData(String pricePolicyId, String userId, String accountId,
                                                             boolean isMainData, IObjectData activityData, BigDecimal storePolicyLimitAmount) {
        IObjectData objectData = new ObjectData();

        objectData.setDescribeApiName(ApiNames.PRICE_POLICY_LIMIT_ACCOUNT_OBJ);
        objectData.setDataOwnDepartment(activityData.getDataOwnDepartment());
        objectData.setDataOwnOrganization(activityData.getDataOwnOrganization());
        objectData.setOutOwner(activityData.getOutOwner());
        objectData.setOwner(activityData.getOwner());
        objectData.setTenantId(activityData.getTenantId());
        objectData.setOutTenantId(activityData.getOutTenantId());
        objectData.setCreatedBy(userId);

        objectData.set(PricePolicyLimitAccountFields.ACTIVITY_ID, activityData.getId());
        objectData.set(PricePolicyLimitAccountFields.PRICE_POLICY_ID, pricePolicyId);
        objectData.set(PricePolicyLimitAccountFields.RANGE, PricePolicyLimitAccountFields.RANGE_POLICY);

        String modeType = activityData.get(TPMActivityFields.MODE_TYPE, String.class);
        String dimension = modeType.endsWith(PricePolicyLimitAccountFields.DIMENSION_GIFT) ? PricePolicyLimitAccountFields.DIMENSION_GIFT : PricePolicyLimitAccountFields.DIMENSION_SELF;
        objectData.set(PricePolicyLimitAccountFields.DIMENSION, dimension);

        if (PricePolicyLimitAccountFields.DIMENSION_GIFT.equals(dimension)) {
            objectData.set(PricePolicyLimitAccountFields.GIFT_TYPE, PricePolicyLimitAccountFields.GIFT_TYPE_ALL);
        }

        String activityLimitObjType = activityData.get(TPMActivityFields.LIMIT_OBJ_TYPE, String.class);
        String limitObjType;
        if (isMainData) {
            limitObjType = PricePolicyLimitAccountFields.LIMIT_OBJ_TYPE_ALL;
        } else {
            limitObjType = activityLimitObjType.startsWith(PricePolicyLimitAccountFields.LIMIT_OBJ_TYPE_ACCOUNT) ? PricePolicyLimitAccountFields.LIMIT_OBJ_TYPE_ACCOUNT : PricePolicyLimitAccountFields.LIMIT_OBJ_TYPE_ALL;
        }
        objectData.set(PricePolicyLimitAccountFields.LIMIT_OBJ_TYPE, limitObjType);

        if (PricePolicyLimitAccountFields.LIMIT_OBJ_TYPE_ACCOUNT.equals(limitObjType)) {
            String accountMode = PricePolicyLimitAccountFields.ACCOUNT_MODE_EQUAL;
            if (TPMActivityFields.LIMIT_OBJ_TYPE_ACCOUNT_COST_DIFFERENCE.equals(activityLimitObjType)) {
                accountMode = PricePolicyLimitAccountFields.ACCOUNT_MODE_DIFFERENCE;
            }
            objectData.set(PricePolicyLimitAccountFields.ACCOUNT_MODE, accountMode);

            if (!Strings.isNullOrEmpty(accountId)) {
                objectData.set(PricePolicyLimitAccountFields.ACCOUNT_ID, accountId);
            }
        }

        String limitType = dimension.equals(PricePolicyLimitAccountFields.DIMENSION_GIFT) ? PricePolicyLimitAccountFields.LIMIT_TYPE_GIFT_AMOUNT : PricePolicyLimitAccountFields.LIMIT_TYPE_ORIGINAL_AMOUNT;
        objectData.set(PricePolicyLimitAccountFields.LIMIT_TYPE, limitType);

        BigDecimal limitNumber = isMainData ? activityData.get(TPMActivityFields.ACTIVITY_AMOUNT, BigDecimal.class, BigDecimal.ZERO) : storePolicyLimitAmount;
        objectData.set(PricePolicyLimitAccountFields.LIMIT_NUMBER, limitNumber);
        // 补充字段, 区分sfa和tpm 来源
        objectData.set(TPMTriggerActionService.REQUEST_FROM, TPMTriggerActionService.REQUEST_APP_NAME);
        return objectData;
    }

    @Override
    public Boolean isOpenPromotionPolicy(String tenantId) {
        String key = String.format("TPM_OPEN_PROMOTION_POLICY_TENANT_%s", tenantId);
        if (redisCmd.exists(key)) {
            return BooleanUtils.toBoolean(redisCmd.get(key));
        }

        GetConfigValueByKey.Arg arg = new GetConfigValueByKey.Arg();
        arg.setKey("price_policy_SalesOrderObj");

        GetConfigValueByKey.Result configValueByKey = paasDataProxy.getConfigValueByKey(Integer.parseInt(tenantId), 1000, arg);
        if (configValueByKey.getCode() != 0) {
            log.error("get sfa config value  err . tenant_id : {}, errMessage : {}", tenantId, configValueByKey.getMessage());
            return false;
        }

        if ("1".equals(configValueByKey.getData().getValue())) {
            redisCmd.setex(key, 3600L, "true");
            return true;
        }
        return false;
    }

    @Override
    public PromotionPolicyEdit.Result doEnableEditPromotionPolicy(String tenantId, IObjectData objectData) {
        String apiName = objectData.getDescribeApiName();
        String id = objectData.getId();

        //入参中的价格规则
        String newProductGiftData = (String) CommonUtils.getOrDefault(objectData.get("product_gift_data_json"), "");
        if (Strings.isNullOrEmpty(newProductGiftData)) {
            log.info("newProductGiftData is empty. tenant_id : {}，activity data id ：{}", tenantId, id);
            return PromotionPolicyEdit.Result.builder().enable(true).build();
        }

        PromotionPolicyPO promotionPolicy = getPromotionPolicyData(tenantId, objectData);
        if (Objects.isNull(promotionPolicy) || Strings.isNullOrEmpty(promotionPolicy.getProductGiftData())) {
            log.info("old promotionPolicy is empty. tenant_id : {}，activity data id ：{}", tenantId, id);
            return PromotionPolicyEdit.Result.builder().enable(true).build();
        }

        //已有的价格规则
        String oldProductGiftData = promotionPolicy.getProductGiftData();
        if (Strings.isNullOrEmpty(oldProductGiftData)) {
            log.info("old ProductGiftData is empty. tenant_id : {}，activity data id ：{}", tenantId, id);
            return PromotionPolicyEdit.Result.builder().enable(true).build();
        }


        if (apiName.equals(ApiNames.TPM_ACTIVITY_OBJ) && Objects.nonNull(objectData.get(TPMActivityFields.ACTIVITY_UNIFIED_CASE_ID, String.class))) {
            if (!Objects.equals(oldProductGiftData, newProductGiftData)) {
                log.info("ProductGiftData old equals new . tenant_id : {}，activity data id ：{}", tenantId, id);
                return PromotionPolicyEdit.Result.builder().enable(false).msg("关联活动方案的活动申请不能编辑活动规则").build();
            }
        }
        List<String> promotionPolicyIds;
        if (ApiNames.TPM_ACTIVITY_OBJ.equals(apiName)) {
            String policyId = promotionPolicy.getPromotionPolicyIdMap().get(objectData.getId());
            promotionPolicyIds = policyId != null ? Lists.newArrayList(policyId) : null;
            String rioActivityId = objectData.get(TPMActivityFields.RIO_ACTIVITY_ID, String.class);
            if (TPMGrayUtils.isRioTenant(tenantId) && StringUtils.isNotEmpty(rioActivityId)) {
                promotionPolicyIds = queryPricePolicyIdsByRIOActivityId(tenantId, rioActivityId);
            }
        } else {
            promotionPolicyIds = new ArrayList<>(promotionPolicy.getPromotionPolicyIdMap().values());
        }

        if (isAlreadyHaveSalesOrder(tenantId, promotionPolicyIds)) {
            return PromotionPolicyEdit.Result.builder().enable(false).msg("已存在订单，不可编辑价格规则").build();
        }
        return PromotionPolicyEdit.Result.builder().enable(true).build();
    }

    private boolean isAlreadyHaveSalesOrder(String tenantId, List<String> promotionPolicyIds) {

        if (CollectionUtils.isEmpty(promotionPolicyIds)) {
            return false;
        }

        SearchTemplateQuery stq = new SearchTemplateQuery();

        stq.setLimit(1);
        stq.setOffset(0);
        stq.setNeedReturnCountNum(false);
        stq.setNeedReturnQuote(false);
        stq.setSearchSource("db");

        Filter activityFilter = new Filter();
        activityFilter.setFieldName(SalesOrderObjFields.PRICE_POLICY_ID);
        activityFilter.setOperator(Operator.IN);
        activityFilter.setFieldValues(Lists.newArrayList(promotionPolicyIds));

        stq.setFilters(Lists.newArrayList(activityFilter));

        //销售订单中价格政策
        List<IObjectData> data = serviceFacade.findBySearchQueryWithFieldsIgnoreAll(
                User.systemUser(tenantId),
                ApiNames.SALES_ORDER_OBJ,
                stq,
                Lists.newArrayList("_id")).getData();

        //订单产品中价格政策
        if (CollectionUtils.isEmpty(data)) {
            data = serviceFacade.findBySearchQueryWithFieldsIgnoreAll(
                    User.systemUser(tenantId),
                    ApiNames.SALES_ORDER_PRODUCT_OBJ,
                    stq,
                    Lists.newArrayList("_id")).getData();
        }
        log.info("relation pricePolicy data size is {}", data.size());
        return CollectionUtils.isNotEmpty(data);
    }

    private List<IObjectData> querySalesOrderByPromotionId(String tenantId, List<String> promotionPolicyIds) {
        SearchTemplateQuery stq = new SearchTemplateQuery();

        stq.setLimit(-1);
        stq.setOffset(0);
        stq.setNeedReturnCountNum(false);
        stq.setNeedReturnQuote(false);
        stq.setSearchSource("db");

        Filter activityFilter = new Filter();
        activityFilter.setFieldName(SalesOrderObjFields.PRICE_POLICY_ID);
        activityFilter.setOperator(Operator.IN);
        activityFilter.setFieldValues(Lists.newArrayList(promotionPolicyIds));

        stq.setFilters(Lists.newArrayList(activityFilter));

        //销售订单中价格政策
        List<IObjectData> data = Lists.newArrayList();
        List<IObjectData> salesDataList = QueryDataUtil.find(serviceFacade, tenantId, ApiNames.SALES_ORDER_OBJ, stq, Lists.newArrayList(CommonFields.ID, SalesOrderObjFields.PRICE_POLICY_ID));
        if (CollectionUtils.isNotEmpty(salesDataList)) {
            data.addAll(salesDataList);
        }

        //订单产品中价格政策
        List<IObjectData> salesOrderDataList = QueryDataUtil.find(serviceFacade, tenantId, ApiNames.SALES_ORDER_PRODUCT_OBJ, stq, Lists.newArrayList(CommonFields.ID, SalesOrderObjFields.PRICE_POLICY_ID));
        if (CollectionUtils.isNotEmpty(salesOrderDataList)) {
            data.addAll(salesOrderDataList);
        }

        return data;
    }

    @Override
    public PromotionPolicyPO getPromotionPolicyData(String tenantId, IObjectData toObjectData) {
        String unifiedActivityId = toObjectData.get(TPMActivityFields.ACTIVITY_UNIFIED_CASE_ID, String.class);
        String objectId = toObjectData.getId();
        String objectApiName = toObjectData.getDescribeApiName();
        if (!Strings.isNullOrEmpty(unifiedActivityId)) {
            objectId = unifiedActivityId;
            objectApiName = ApiNames.TPM_ACTIVITY_UNIFIED_CASE_OBJ;
        }
        return promotionPolicyDAO.find(tenantId, objectId, objectApiName);
    }

    public List<PromotionPolicyPO> queryPromotionPolicyData(String tenantId, List<IObjectData> objectDatas, String apiName) {
        List<String> activityIds = Lists.newArrayList();
        List<String> activityUnifiedCaseIds = Lists.newArrayList();

        if (ApiNames.TPM_ACTIVITY_UNIFIED_CASE_OBJ.equals(apiName)) {
            activityUnifiedCaseIds = objectDatas.stream().map(IObjectData::getId).collect(Collectors.toList());
        }
        if (ApiNames.TPM_ACTIVITY_OBJ.equals(apiName)) {

            activityUnifiedCaseIds = objectDatas.stream().map(objectData -> {
                String activityUnifiedCaseId = objectData.get(TPMActivityFields.ACTIVITY_UNIFIED_CASE_ID, String.class, "");
                if (!Strings.isNullOrEmpty(activityUnifiedCaseId)) {
                    return activityUnifiedCaseId;
                } else {
                    return null;
                }
            }).filter(Objects::nonNull).distinct().collect(Collectors.toList());

            List<String> finalActivityUnifiedCaseIds = activityUnifiedCaseIds;
            activityIds = objectDatas.stream().map(IObjectData::getId).filter(id -> CollectionUtils.isEmpty(finalActivityUnifiedCaseIds) || !finalActivityUnifiedCaseIds.contains(id)).distinct().collect(Collectors.toList());
        }


        List<PromotionPolicyPO> promotionPolicies = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(activityIds)) {
            List<PromotionPolicyPO> policyPOS = promotionPolicyDAO.queryByObjectIds(tenantId, activityIds, ApiNames.TPM_ACTIVITY_OBJ);
            policyPOS = policyPOS.stream().distinct().collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(policyPOS)) {
                promotionPolicies.addAll(policyPOS);
            }
        }

        if (CollectionUtils.isNotEmpty(activityUnifiedCaseIds)) {
            List<PromotionPolicyPO> policyPOS = promotionPolicyDAO.queryByObjectIds(tenantId, activityUnifiedCaseIds, ApiNames.TPM_ACTIVITY_UNIFIED_CASE_OBJ);
            policyPOS = policyPOS.stream().distinct().collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(policyPOS)) {
                promotionPolicies.addAll(policyPOS);
            }
        }

        return promotionPolicies;
    }

    @Override
    public PromotionPolicyEdit.Result enableEditPromotionPolicy(PromotionPolicyEdit.Arg arg) {
        ApiContext context = ApiContextManager.getContext();
        String tenantId = context.getTenantId();

        String objectApiName = arg.getObjectApiName();
        String id = arg.getObjectId();

        IObjectData objectData = serviceFacade.findObjectDataByIdsIgnoreAll(tenantId, Lists.newArrayList(id), objectApiName).get(0);
        if (Objects.isNull(objectData)) {
            return PromotionPolicyEdit.Result.builder().enable(false).msg("数据不存在").build();
        }
        if (objectApiName.equals(ApiNames.TPM_ACTIVITY_OBJ) && !Strings.isNullOrEmpty(objectData.get(TPMActivityFields.ACTIVITY_UNIFIED_CASE_ID, String.class))) {
            return PromotionPolicyEdit.Result.builder().enable(false).msg("关联活动方案的活动申请不能编辑活动规则").build();
        }
        return doEnableEditPromotionPolicy(tenantId, objectData);
    }

    /**
     * 判断是否为促销数据
     *
     * @return boolean true/ false
     */
    @Override
    public boolean judgedIsPromotionPolicyData(IObjectData objectData) {
        String activityTypeId = objectData.get(TPMActivityFields.ACTIVITY_TYPE, String.class, "");
        if (Objects.isNull(activityTypeId)) {
            return false;
        }
        ActivityTypeExt activityTypeExt = activityTypeManager.find(objectData.getTenantId(), activityTypeId);
        if (Objects.isNull(activityTypeExt)) {
            return false;
        }
        String templateId = activityTypeExt.get().getTemplateId();
        if (Objects.isNull(templateId)) {
            return false;
        }
        log.info("activityTypeExt get templateId is {}", templateId);
        return templateId.startsWith("promotion");
    }

    @Override
    public boolean doEnableInvalidPromotionPolicy(String tenantId, IObjectData objectData) {

        PromotionPolicyPO promotionPolicy = getPromotionPolicyData(tenantId, objectData);
        if (Objects.isNull(promotionPolicy) || Strings.isNullOrEmpty(promotionPolicy.getProductGiftData())) {
            return false;
        }

        Map<String, String> promotionPolicyIdMap = promotionPolicy.getPromotionPolicyIdMap();
        if (CollectionUtils.isEmpty(promotionPolicyIdMap.values())) {
            return false;
        }

        List<String> policyIds;
        String describeApiName = objectData.getDescribeApiName();
        if (ApiNames.TPM_ACTIVITY_OBJ.equals(describeApiName)) {
            String policyId = promotionPolicy.getPromotionPolicyIdMap().get(objectData.getId());
            policyIds = policyId != null ? Lists.newArrayList(policyId) : null;

            String rioActivityId = objectData.get(TPMActivityFields.RIO_ACTIVITY_ID, String.class);
            if (TPMGrayUtils.isRioTenant(tenantId) && StringUtils.isNotEmpty(rioActivityId)) {
                policyIds = queryPricePolicyIdsByRIOActivityId(tenantId, rioActivityId);
            }
        } else {
            policyIds = new ArrayList<>(promotionPolicyIdMap.values());
        }

        return isAlreadyHaveSalesOrder(tenantId, policyIds);
    }


    public Map<String, List<IObjectData>> queryIsNoSalesOrderActivityMap(String tenantId, List<IObjectData> objectDatas, String apiName) {
        if (CollectionUtils.isEmpty(objectDatas)) {
            return Maps.newHashMap();
        }

        //过滤只与促销有关的活动方案或活动申请(ACTIVITY_TYPE 活动类型)
        Map<String, List<IObjectData>> activityTypeIdActivityObjMap = objectDatas.stream()
                .filter(o -> !Strings.isNullOrEmpty(o.get(TPMActivityUnifiedCaseFields.ACTIVITY_TYPE, String.class, "")))
                .collect(Collectors.groupingBy(o -> o.get(TPMActivityUnifiedCaseFields.ACTIVITY_TYPE, String.class)));

        List<ActivityTypeExt> activityTypeExts = activityTypeManager.findByActivityTypeIds(tenantId, new ArrayList<>(activityTypeIdActivityObjMap.keySet()));
        if (CollectionUtils.isEmpty(activityTypeExts)) {
            return Maps.newHashMap();
        }
        List<String> promotionActivityTypeIds = activityTypeExts.stream()
                .filter(type -> {
                    String templateId = type.get().getTemplateId();
                    return !Strings.isNullOrEmpty(templateId) && templateId.startsWith("promotion");
                })
                .map(etx -> etx.get().getUniqueId()).collect(Collectors.toList());

        List<IObjectData> hasPromotionPolicyActivity = Lists.newArrayList();
        promotionActivityTypeIds.forEach(typeId -> {
            List<IObjectData> activities = activityTypeIdActivityObjMap.get(typeId);
            if (CollectionUtils.isNotEmpty(activities)) {
                hasPromotionPolicyActivity.addAll(activities);
            }
        });
        if (CollectionUtils.isEmpty(hasPromotionPolicyActivity)) {
            return Maps.newHashMap();
        }

        if (TPMGrayUtils.isRioTenant(tenantId)) {
            List<IObjectData> rioActivitys = hasPromotionPolicyActivity.stream()
                    .filter(o -> StringUtils.isNotEmpty(o.get(TPMActivityFields.RIO_ACTIVITY_ID, String.class)))
                    .collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(rioActivitys)) {
                List<String> rioActivityIds = rioActivitys.stream().map(o -> o.get(TPMActivityFields.RIO_ACTIVITY_ID, String.class)).distinct().collect(Collectors.toList());
                //有rio自定义活动的活动申请
                List<IObjectData> hasRioActivityActivities = queryActivityByRioActivityIds(tenantId, rioActivityIds);
                List<String> activityIds = rioActivitys.stream().map(IObjectData::getId).distinct().collect(Collectors.toList());
                hasRioActivityActivities = hasRioActivityActivities.stream().filter(o -> !activityIds.contains(o.getId())).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(hasRioActivityActivities)) {
                    hasPromotionPolicyActivity.addAll(hasRioActivityActivities);
                }
            }
        }
        List<PromotionPolicyPO> promotionPolicys = queryPromotionPolicyData(tenantId, hasPromotionPolicyActivity, apiName);
        if (CollectionUtils.isEmpty(promotionPolicys)) {
            return Maps.newHashMap();
        }

        List<String> promotionPolicyIds = promotionPolicys.stream().map(o -> o.getPromotionPolicyIdMap().values()).collect(Collectors.toList())
                .stream().flatMap(Collection::stream).distinct().collect(Collectors.toList());

        //有订单活动和无订单活动
        List<IObjectData> salesIsNotEmptyActivity = Lists.newArrayList();
        List<IObjectData> salesIsEmptyActivity = Lists.newArrayList();
        List<IObjectData> allPromotionActivity = Lists.newArrayList();
        List<IObjectData> dataList = querySalesOrderByPromotionId(tenantId, promotionPolicyIds);

        Map<String, IObjectData> idActivityMap = objectDatas.stream().collect(Collectors.toMap(IObjectData::getId, o -> o));
        //订单中所有价格政策id
        List<String> pricePolicyIds = dataList.stream().map(o -> o.get(SalesOrderObjFields.PRICE_POLICY_ID, String.class)).distinct().collect(Collectors.toList());

        for (PromotionPolicyPO promotionPolicy : promotionPolicys) {
            Map<String, String> promotionPolicyIdMap = promotionPolicy.getPromotionPolicyIdMap();
            if (promotionPolicyIdMap.isEmpty()) {
                continue;
            }

            if (ApiNames.TPM_ACTIVITY_OBJ.equals(apiName)) {
                for (Map.Entry<String, String> activityPromotionPolicyId : promotionPolicyIdMap.entrySet()) {
                    //价格政策中的活动申请
                    String activityId = activityPromotionPolicyId.getKey();
                    if (idActivityMap.containsKey(activityId)) {
                        if (CollectionUtils.isNotEmpty(pricePolicyIds) && pricePolicyIds.contains(activityPromotionPolicyId.getValue())) {
                            salesIsNotEmptyActivity.add(idActivityMap.get(activityId));
                        } else {
                            salesIsEmptyActivity.add(idActivityMap.get(activityId));
                        }
                        allPromotionActivity.add(idActivityMap.get(activityId));
                    }
                }
            }

            if (ApiNames.TPM_ACTIVITY_UNIFIED_CASE_OBJ.equals(apiName)) {
                String objectId = promotionPolicy.getObjectId();
                List<String> promotionPolicyIdList = new ArrayList<>(promotionPolicyIdMap.values());
                if (CollectionUtils.containsAny(pricePolicyIds, promotionPolicyIdList)) {
                    salesIsNotEmptyActivity.add(idActivityMap.get(objectId));
                } else {
                    salesIsEmptyActivity.add(idActivityMap.get(objectId));
                }
                allPromotionActivity.add(idActivityMap.get(objectId));
            }
        }

        Map<String, List<IObjectData>> typeOfActivityObjs = Maps.newHashMap();
        typeOfActivityObjs.put("salesIsNotEmptyActivity", salesIsNotEmptyActivity);
        typeOfActivityObjs.put("salesIsEmptyActivity", salesIsEmptyActivity);
        typeOfActivityObjs.put("allPromotionActivity", allPromotionActivity);
        return typeOfActivityObjs;
    }

    private List<IObjectData> queryActivityByRioActivityIds(String tenantId, List<String> rioActivityIds) {
        SearchTemplateQuery query = new SearchTemplateQuery();

        query.setLimit(-1);
        query.setOffset(0);
        query.setNeedReturnQuote(false);
        query.setNeedReturnCountNum(false);
        query.setSearchSource("db");

        Filter activityIdFilter = new Filter();
        activityIdFilter.setFieldName(TPMActivityFields.RIO_ACTIVITY_ID);
        activityIdFilter.setOperator(Operator.IN);
        activityIdFilter.setFieldValues(rioActivityIds);

        query.setFilters(Lists.newArrayList(activityIdFilter));

        return QueryDataUtil.find(serviceFacade, tenantId, ApiNames.TPM_ACTIVITY_OBJ, query, Lists.newArrayList());
    }

    /**
     * 判断是否需要活动申请， 禁止手动创建带有活动的价格政策相关对象数据
     *
     * @return Boolean true / false
     */
    @Override
    public boolean validationIsNeedActivityId(IObjectData objectData) {
        if (isFromTPM(objectData)) {
            return true;
        }

        String activity = objectData.get(PricePolicyFields.ACTIVITY_ID, String.class, "");
        if (Strings.isNullOrEmpty(activity)) {
            String rioActivityId = objectData.get(PricePolicyLimitAccountFields.RIO_ACTIVITY_ID, String.class);
            return Strings.isNullOrEmpty(rioActivityId);
        } else {
            return false;
        }
    }

    @Override
    public boolean validationIsInvalid(IObjectData objectData) {
        if (isFromTPM(objectData)) {
            return true;
        }

        String activityId = objectData.get(PricePolicyFields.ACTIVITY_ID, String.class, "");
        if (Strings.isNullOrEmpty(activityId)) {
            String rioActivityId = objectData.get(PricePolicyLimitAccountFields.RIO_ACTIVITY_ID, String.class, "");
            return StringUtils.isEmpty(rioActivityId);
        }
        IObjectData tpmActivityObj = null;
        try {
            IObjectDescribe activityDescribe = serviceFacade.findObject(objectData.getTenantId(), ApiNames.TPM_ACTIVITY_OBJ);
            tpmActivityObj = serviceFacade.findObjectDataIncludeNull(objectData.getTenantId(), activityId, activityDescribe);
        } catch (Exception e) {
            log.error("validationIsInvalid find  err,tenantId = {},activityId = {} ", objectData.getTenantId(), activityId, e);
        }

        return Objects.isNull(tpmActivityObj);
    }

    @Override
    public boolean pricePolicyIsContainsActivity(IObjectData objectData) {
        if (isFromTPM(objectData)) {
            return false;
        }
        String pricePolicyId = objectData.get(PricePolicyLimitAccountFields.PRICE_POLICY_ID, String.class);
        if (Objects.nonNull(pricePolicyId)) {
            IObjectData data = serviceFacade.findObjectDataIgnoreAll(User.systemUser(objectData.getTenantId()), pricePolicyId, ApiNames.PRICE_POLICY_OBJ);
            String activityId = data.get(PricePolicyFields.ACTIVITY_ID, String.class, "");
            return StringUtils.isNotEmpty(activityId);
        }

        List<String> pricePolicyIds = objectData.get(PricePolicyLimitAccountFields.PRICE_POLICY_IDS, List.class);
        if (CollectionUtils.isNotEmpty(pricePolicyIds)) {
            List<IObjectData> pricePolicyData = serviceFacade.findObjectDataByIds(objectData.getTenantId(), pricePolicyIds, ApiNames.PRICE_POLICY_OBJ);
            return pricePolicyData.stream().anyMatch(data -> StringUtils.isNotEmpty(data.get(PricePolicyFields.ACTIVITY_ID, String.class, "")));
        }
        return false;
    }

    @Override
    public List<String> queryIsNotValidaNames(List<ObjectDataDocument> objectDataList) {
        if (CollectionUtils.isEmpty(objectDataList)) {
            return Lists.newArrayList();
        }

        IObjectData objectData = objectDataList.get(0).toObjectData();
        if (isFromTPM(objectData)) {
            return Lists.newArrayList();
        }

        String apiName = objectData.getDescribeApiName();

        List<String> excludeNames = Lists.newArrayList();
        if (ApiNames.PRICE_POLICY_OBJ.equals(apiName)) {

            List<IObjectData> dataList = ObjectDataDocument.ofDataList(objectDataList);

            List<String> invalidActivityIds = getInvalidActivityIds(objectData.getTenantId(), dataList);

            for (ObjectDataDocument document : objectDataList) {
                IObjectData data = document.toObjectData();
                String activityId = data.get(PricePolicyFields.ACTIVITY_ID, String.class, "");
                if (!Strings.isNullOrEmpty(activityId) && !invalidActivityIds.contains(activityId)) {
                    excludeNames.add(data.getName());
                }
            }
        }

        if (ApiNames.PRICE_POLICY_LIMIT_ACCOUNT_OBJ.equals(apiName)) {
            Map<String, List<ObjectDataDocument>> policyIdPolicyLimitMap = objectDataList.stream()
                    .filter(objectDataDocument -> StringUtils.isNotEmpty(objectDataDocument.toObjectData().get(PricePolicyLimitAccountFields.PRICE_POLICY_ID, String.class)))
                    .collect(Collectors.groupingBy(objectDataDocument -> objectDataDocument.toObjectData().get(PricePolicyLimitAccountFields.PRICE_POLICY_ID, String.class)));

            List<ObjectDataDocument> hasRioActivitiesPolicyLimitList = objectDataList.stream()
                    .filter(objectDataDocument -> CollectionUtils.isNotEmpty(objectDataDocument.toObjectData().get(PricePolicyLimitAccountFields.PRICE_POLICY_IDS, List.class)))
                    .collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(hasRioActivitiesPolicyLimitList)) {
                for (ObjectDataDocument objectDataDocument : hasRioActivitiesPolicyLimitList) {
                    List<String> pricePolicyIds = objectDataDocument.toObjectData().get(PricePolicyLimitAccountFields.PRICE_POLICY_IDS, List.class);
                    for (String pricePolicyId : pricePolicyIds) {
                        policyIdPolicyLimitMap.put(pricePolicyId, Lists.newArrayList(objectDataDocument));
                    }
                }
            }

            List<String> pricePolicyIds = policyIdPolicyLimitMap.keySet().stream().filter(StringUtils::isNotEmpty).collect(Collectors.toList());

            List<IObjectData> dataList = serviceFacade.findObjectDataByIdsIgnoreAll(objectData.getTenantId(), pricePolicyIds, ApiNames.PRICE_POLICY_OBJ);
            if (CollectionUtils.isEmpty(dataList)) {
                return excludeNames;
            }

            List<String> invalidActivityIds = getInvalidActivityIds(objectData.getTenantId(), dataList);

            for (IObjectData data : dataList) {
                String activityId = data.get(PricePolicyFields.ACTIVITY_ID, String.class, "");
                if (!Strings.isNullOrEmpty(activityId) && !invalidActivityIds.contains(activityId)) {
                    List<String> names = policyIdPolicyLimitMap.get(data.getId())
                            .stream()
                            .map(objectDataDocument -> objectDataDocument.toObjectData().getName())
                            .collect(Collectors.toList());
                    if (CollectionUtils.isNotEmpty(names)) {
                        excludeNames.addAll(names);
                    }
                }
            }
        }
        return excludeNames.stream().distinct().collect(Collectors.toList());
    }

    @NotNull
    private List<String> getInvalidActivityIds(String tenantId, List<IObjectData> dataList) {
        List<String> activityIds = dataList.stream().filter(o -> {
            String activityId = o.get(PricePolicyFields.ACTIVITY_ID, String.class, "");
            return !Strings.isNullOrEmpty(activityId);
        }).map(o -> o.get(PricePolicyFields.ACTIVITY_ID, String.class)).collect(Collectors.toList());

        if (CollectionUtils.isEmpty(activityIds)) {
            return Lists.newArrayList();
        }
        List<IObjectData> activities = serviceFacade.findObjectDataByIdsIncludeDeleted(User.systemUser(tenantId), activityIds, ApiNames.TPM_ACTIVITY_OBJ);

        return activities.stream()
                .filter(obj -> LifeStatusEnum.Invalid.getValue().equals(obj.get(TPMActivityFields.LIFE_STATUS, String.class, "")))
                .map(IObjectData::getId).collect(Collectors.toList());
    }

    private boolean isFromTPM(IObjectData objectData) {
        String requestFrom = objectData.get(TPMTriggerActionService.REQUEST_FROM, String.class);
        return TPMTriggerActionService.REQUEST_APP_NAME.equals(requestFrom);
    }

    private List<String> queryPricePolicyIdsByRIOActivityId(String tenantId, String rioActivityId) {
        if (Strings.isNullOrEmpty(rioActivityId)) {
            return Lists.newArrayList();
        }
        SearchTemplateQuery query = new SearchTemplateQuery();

        query.setLimit(-1);
        query.setOffset(0);
        query.setNeedReturnQuote(false);
        query.setNeedReturnCountNum(false);
        query.setSearchSource("db");

        Filter activityIdFilter = new Filter();
        activityIdFilter.setFieldName(PricePolicyLimitAccountFields.RIO_ACTIVITY_ID);
        activityIdFilter.setOperator(Operator.EQ);
        activityIdFilter.setFieldValues(Lists.newArrayList(rioActivityId));

        query.setFilters(Lists.newArrayList(activityIdFilter));
        List<IObjectData> limitAccountList = QueryDataUtil.find(serviceFacade, tenantId, ApiNames.PRICE_POLICY_LIMIT_ACCOUNT_OBJ, query, Lists.newArrayList(PricePolicyLimitAccountFields.PRICE_POLICY_IDS));
        if (CollectionUtils.isEmpty(limitAccountList)) {
            return Lists.newArrayList();
        }
        return limitAccountList.get(0).get(PricePolicyLimitAccountFields.PRICE_POLICY_IDS, List.class);
    }

}
