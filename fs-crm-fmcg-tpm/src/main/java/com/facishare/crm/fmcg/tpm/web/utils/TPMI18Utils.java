package com.facishare.crm.fmcg.tpm.web.utils;

import com.facishare.paas.I18N;
import com.google.common.base.Strings;
import lombok.experimental.UtilityClass;

/**
 * description : just code
 * <p>
 * create by @wuyx
 * create time 2021/11/24 17:06
 */
@UtilityClass
public class TPMI18Utils {


    public static final String TERMINAL_KEY = "fs.fmcg.tpm";
    public static final String TERMINAL_KEY_BUDGET = "fs.fmcg.budget";

    public static String getPlatActivityI18nKey(String tenantId, String id) {
        return getKey(TERMINAL_KEY, tenantId, id);
    }

    public static String getPlatBudgetI18nKey(String tenantId, String id) {
        return getKey(TERMINAL_KEY_BUDGET, tenantId, id);
    }


    public static String getActivitySystemNodeNameI18nKey(String uniqueId) {
        return getKey("fmcg.activity.node.system", null, uniqueId);
    }

    public static String getActivitySystemTypeNameI18nKey(String uniqueId) {
        return getKey("fmcg.activity.type.system", null, uniqueId);
    }

    public static String getKey(String terminalKey, String tenantId, String uniqueId) {
        if (Strings.isNullOrEmpty(tenantId)) {
            return String.format("%s.%s", terminalKey, uniqueId);
        }
        return String.format("%s.%s.%s", terminalKey, tenantId, uniqueId);
    }

    public static String getActivitySystemNodeText(String tenantId, String nodeId, String objectApiName) {
        if (!Strings.isNullOrEmpty(nodeId)) {
            String i18nKey = TPMI18Utils.getPlatActivityI18nKey(tenantId, nodeId);
            String text = I18N.text(i18nKey);
            if (!Strings.isNullOrEmpty(text)) {
                return text;
            }
        }
        return I18N.text(TPMI18Utils.getActivitySystemNodeNameI18nKey(objectApiName));
    }

    public static String getActivitySystemTypeText(String tenantId, String typeId, String apiMame) {
        if (!Strings.isNullOrEmpty(typeId)) {
            String i18nKey = TPMI18Utils.getPlatActivityI18nKey(tenantId, typeId);
            String text = I18N.text(i18nKey);
            if (!Strings.isNullOrEmpty(text)) {
                return text;
            }
        }
        return I18N.text(TPMI18Utils.getActivitySystemTypeNameI18nKey(apiMame));
    }

    public static String getBudgeText(String tenantId, String id) {
        if (!Strings.isNullOrEmpty(id)) {
            String i18nKey = TPMI18Utils.getPlatBudgetI18nKey(tenantId, id);
            String text = I18N.text(i18nKey);
            if (!Strings.isNullOrEmpty(text)) {
                return text;
            }
        }
        return null;
    }

}
